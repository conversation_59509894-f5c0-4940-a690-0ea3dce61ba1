<script setup>
import { nextTick, ref, computed, h } from 'vue'
import {
  NInput,
  NButton,
  NDataTable,
  NModal,
  NGrid,
  NGi,
  useMessage,
  NSpace,
  NIcon,
  NCarousel,
} from 'naive-ui'
import { AddCircleOutline, Close, Add, EyeOutline, CreateOutline } from '@vicons/ionicons5'
import CommonPage from '@/components/page/CommonPage.vue'
import api from '@/api'
import * as XLSX from 'xlsx'
import CarModelSelector from '../../../components/vehicleComponents/CarModelSelector.vue'
import OESelector from '@/components/oeComponents/OESelector.vue'
defineOptions({ name: '产品数据查询' })

const message = useMessage()

const queryItems = ref({
  MaterialCode: null,
  ChineseName: null,
  Attributes: null,
  Availability: null,
})

const ChineseName_options = ref([])
const Attributes_options = ref([])
const Availability_options = ref([])
const search_loading = ref(false)
const rawProductData = ref([]) // 存储从后端获取的原始数据
const productTypeSuffixMap = ref({}) // 存储处理后的 {产品类型: [尾缀数组]} 映射
const selectedVehicles = ref([]) // 当前选中的车型
const selectedOEs = ref([]) // 当前选中的oe号
const selectedMainNum = ref([]) // 当前选中的主号
const selectedCompetitors = ref([]) // 当前选中的竞品号
const carIdsFromBindOeSelector = ref([]) // 来自OE弹窗绑定的车型ids

// 接收选择的车型数据
const handleCarModelSelect = (selectVehicleRows) => {
  // 1. 获取当前已选车辆的 reach_car_id 集合（用于去重判断）
  const existingCarIds = new Set(selectedVehicles.value.map((vehicle) => vehicle.reach_car_id))

  // 2. 过滤新选中的车辆，只保留不存在于 selectedVehicles 的
  const newVehicles = selectVehicleRows.rows.filter(
    (vehicle) => !existingCarIds.has(vehicle.reach_car_id)
  )

  // 3. 合并：老数据 + 新的不重复数据
  selectedVehicles.value = [...selectedVehicles.value, ...newVehicles]

  // 4. 关闭弹窗
  showVehicleSelector.value = false
}

// 接收选择的OE数据
const handleOESelect = (selectOERows) => {
  showOESelector.value = false // 选择后自动关闭
  // 获取当前已有的 oe_num 集合，用于快速判断
  const existingOeNums = new Set(selectedOEs.value.map((oe) => oe.oe_num))
  // 过滤出 selectOERows.rows 中 oe_num 不存在于 selectedOEs 的项
  const newOERows = selectOERows.rows.filter((row) => !existingOeNums.has(row.oe_num))
  // 追加新项
  selectedOEs.value = [...selectedOEs.value, ...newOERows]
  // 同样，将新 OE 关联的车型也去重后添加
  const newVehicles = []
  const existingVehicleIds = new Set(selectedVehicles.value.map((car) => car.reach_car_id))

  for (const row of newOERows) {
    if (Array.isArray(row.vehicle_info)) {
      for (const reach_car_id of row.vehicle_info) {
        if (!existingVehicleIds.has(reach_car_id)) {
          existingVehicleIds.add(reach_car_id)
          newVehicles.push(reach_car_id)
        }
      }
    }
  }

  selectedVehicles.value.push(...newVehicles)
}

const handleOESelectMainNum = (payload) => {
  showOESelector.value = false // 选择后自动关闭
  console.log('------->selectedMainNumRow为', payload.selectedRows)
  // 将拿到的payload.selectedRows，推入selectedMainNum中
  const rows = payload?.selectedRows || []
  if (Array.isArray(rows)) {
    selectedMainNum.value = rows
  }
  // 获取从弹窗中绑定主号的车型ids
  carIdsFromBindOeSelector.value = payload.oeCarIds
}

const fetchProductSuffixData = async () => {
  try {
    // 模拟 API 调用 (替换为你的实际 API 端点)
    const response = await api.ProductCategorySuffix()
    const suffixData = await response.data
    rawProductData.value = suffixData

    // **关键步骤: 将原始数据转换成 {产品类型: [尾缀数组]} 的对象格式**
    const map = {}
    rawProductData.value.forEach((item) => {
      // 假设每个对象只有一个键 (产品类型)
      const productType = Object.keys(item)[0]
      const suffixes = item[productType]
      map[productType] = suffixes
    })
    productTypeSuffixMap.value = map
    console.log('产品类型-尾缀映射:', productTypeSuffixMap.value)
  } catch (error) {
    console.error('获取产品数据失败:', error)
    // 处理错误 (例如显示错误消息)
  }
}

// n-select 的 :options 需要一个对象数组，每个对象通常有 label 和 value 属性
const productTypeOptions = computed(() => {
  return Object.keys(productTypeSuffixMap.value).map((type) => ({
    label: type, // 显示给用户看的文本
    value: type, // 实际绑定的值 (就是产品类型本身)
  }))
})

// 为尾缀下拉框准备选项
const suffixOptions = computed(() => {
  const selectedCategory = formData.value.basic.category
  if (!selectedCategory) return []
  return productTypeSuffixMap.value[selectedCategory]?.map(suffix => ({
    label: suffix,
    value: suffix
  })) || []
})
// 当品类改变时，清空 suffix
const handleCategoryChange = (newCategory) => {
  // 清空尾缀
  formData.value.basic.suffix = null
}
const fetchInitOptions = async () => {
  search_loading.value = true
  try {
    const api_options = await api.productOptionList()
    ChineseName_options.value = api_options.data.ChineseName || []
    Attributes_options.value = api_options.data.Attributes || []
    Availability_options.value = api_options.data.Availability || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    search_loading.value = false // 完成加载
  }
}

const handleOptionChange = async (optionKey, selectedValue) => {
  search_loading.value = true
  try {
    queryItems.value[optionKey] = selectedValue
    // 从后端获取并更新其它选项
    const updatedOptions = await api.productOptionList(queryItems.value)
    ChineseName_options.value = updatedOptions.data.ChineseName || []
    Attributes_options.value = updatedOptions.data.Attributes || []
    Availability_options.value = updatedOptions.data.Availability || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    search_loading.value = false
  }
}

// const pagination = ref({
//   page: 1,
//   pageSize: 10,
//   showSizePicker: true,
//   pageSizes: [10, 20, 50],
// })
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 10,
  pageSizes: [10, 20, 50, 100, 500],
  showSizePicker: true,
})

async function fetchData() {
  try {
    const params = {
      ...queryItems.value,
      page: pagination.page,
      pageSize: pagination.pageSize,
    }
    const res = await api.productDataList(params)
    tableData.value = res.data
    pagination.itemCount = res.total
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

async function handleSearch() {
  pagination.page = 1
  await fetchData()
}

function handlePageChange(page) {
  pagination.page = page
  fetchData()
}

function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchData()
}

const handleUpdatePage = (page) => {
  handlePageChange(page)
}

const handleUpdatePageSize = (pageSize) => {
  handlePageSizeChange(pageSize)
}

async function customReset() {
  // 清空 queryItems 的内容
  queryItems.value = {
    MaterialCode: null,
    ChineseName: null,
    Attributes: null,
    Availability: null,
  }
  // 重新填充所有下拉框的数据
  await fetchInitOptions()
  await handleSearch()
}

// localStorage 键名
const LOCAL_STORAGE_KEY = 'MY_DYNAMIC_TABLE_VISIBLE_COLUMNS'

// Popover 实例引用
const columnSelectorPopoverRef = ref(null)

// 1. 定义所有可能的列
const allColumns = [
  {
    title: '物料代码',
    key: 'MaterialCode',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '主号',
    key: 'MainNo',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: '品牌',
    key: 'Brand',
    width: 80,
    ellipsis: { tooltip: true },
  },
  {
    title: '中文名称',
    key: 'ChineseName',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: '规格型号',
    key: 'SpecModel',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '属性',
    key: 'Attributes',
    width: 80,
    ellipsis: { tooltip: true },
  },
  {
    title: '可供状态',
    key: 'Availability',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '适用市场',
    key: 'Market',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '车系英文',
    key: 'SeriesEN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '车型英文',
    key: 'ModelEN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '车系中文',
    key: 'SeriesCN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '车型中文',
    key: 'ModelCN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '内销OE',
    key: 'DomesticOE',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: '内销标签车型',
    key: 'DomesticLabelModel',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '有无照片',
    key: 'HasPhoto',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '是否同步007',
    key: 'Sync007',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '007优良等级',
    key: 'QualityLevel007',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '参考号',
    key: 'ReferenceNo',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '是否做目录',
    key: 'IsCataloged',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '是否新品发布',
    key: 'IsNewRelease',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '新品发布日期',
    key: 'NewReleaseDate',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: 'EAN号',
    key: 'EAN',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: 'HS编码',
    key: 'HSCode',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '长',
    key: 'Length',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '宽',
    key: 'Width',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '高',
    key: 'Height',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '体积',
    key: 'Volume',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '替代说明',
    key: 'ReplacementDesc',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '供应商名称',
    key: 'SupplierName',
    width: 250,
    ellipsis: { tooltip: true },
  },
  {
    title: '默认供应商ID',
    key: 'DefaultSupplierID',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '备注',
    key: 'Remarks',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    width: 130,
    fixed: 'right',
    render(row) {
      return h(
        NSpace,
        {
          justify: 'center',
        },
        {
          default: () => [
            h(
              NButton,
              {
                size: 'small',
                quaternary: true,
                circle: true,
                // text: true, // 另一种纯文本/图标按钮样式
                title: '详情',
                onClick: () => Detail(row),
              },
              {
                default: () => h(NIcon, null, { default: () => h(EyeOutline) }),
              }
            ),
            h(
              NButton,
              {
                size: 'small',
                quaternary: true,
                circle: true,
                // text: true,
                type: 'primary',
                title: '编辑',
                style: { marginLeft: '0px' },
                onClick: () => handleEditClick(row),
              },
              {
                default: () => h(NIcon, null, { default: () => h(CreateOutline) }),
              }
            ),
          ],
        }
      )
    },
  },
]

// 表格数据 (多造几条用于测试滚动和固定列)
const tableData = ref([])

// 2. 维护实际可见列的键 (从 localStorage 初始化或使用默认)
const visibleColumnKeys = ref([])
// 临时存储在 Popover 中选择的列，点击"确定"后才应用
const tempVisibleColumnKeys = ref([])

// 默认显示的列 (例如，选择所有列，或者一个预设的子集)
const getDefaultVisibleKeys = () => {
  //   allColumns.map((col) => col.key)
  return [
    'MaterialCode',
    'DomesticOE',
    'ChineseName',
    'SpecModel',
    'ReplacementDesc',
    'SupplierName',
    'Availability',
    'actions',
  ]
}
// 初始化 visibleColumnKeys
const initializeVisibleColumns = () => {
  const savedKeysJson = localStorage.getItem(LOCAL_STORAGE_KEY)
  let keysToUse = getDefaultVisibleKeys() // 默认值

  if (savedKeysJson) {
    try {
      const savedKeys = JSON.parse(savedKeysJson)
      if (Array.isArray(savedKeys) && savedKeys.every((key) => typeof key === 'string')) {
        // 过滤掉在 allColumns 中已不存在的 key
        const validSavedKeys = savedKeys.filter((key) => allColumns.some((col) => col.key === key))
        if (validSavedKeys.length > 0) {
          keysToUse = validSavedKeys
        }
      }
    } catch (error) {
      console.error('从localStorage加载列配置失败:', error)
      // 出错则使用默认值
    }
  }
  visibleColumnKeys.value = [...keysToUse]
  tempVisibleColumnKeys.value = [...keysToUse] // 初始化临时选择
}

onMounted(() => {
  fetchInitOptions()
  fetchData()
  initializeVisibleColumns()
  fetchProductSuffixData()
  loadSupplierData()
})

// 3. 计算实际显示的列 (基于 visibleColumnKeys)
const displayColumns = computed(() => {
  const dcolumns = allColumns
    .filter((column) => visibleColumnKeys.value.includes(column.key))
    .sort((a, b) => {
      // 保持原始顺序
      const indexA = allColumns.findIndex((col) => col.key === a.key)
      const indexB = allColumns.findIndex((col) => col.key === b.key)
      return indexA - indexB
    })
  dcolumns.unshift({
    type: 'selection',
    fixed: 'left',
  })
  return dcolumns
})

// 4. Popover 相关逻辑å
const handlePopoverUpdateShow = (show) => {
  if (show) {
    // Popover 打开时，用当前的实际显示列重置临时选择
    tempVisibleColumnKeys.value = [...visibleColumnKeys.value]
  }
}

const applyColumnSelection = () => {
  if (tempVisibleColumnKeys.value.length === 0) {
    message.error('至少需要选择一列！')
    return
  }
  visibleColumnKeys.value = [...tempVisibleColumnKeys.value]
  localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(visibleColumnKeys.value))
  message.success('列配置已更新')
  columnSelectorPopoverRef.value?.setShow(false) // 关闭 Popover
}

const cancelColumnSelection = () => {
  // 重置临时选择为当前实际的列，然后关闭
  tempVisibleColumnKeys.value = [...visibleColumnKeys.value]
  columnSelectorPopoverRef.value?.setShow(false) // 关闭 Popover
}

const resetToDefaultColumns = () => {
  tempVisibleColumnKeys.value = getDefaultVisibleKeys()
  // 如果希望重置后立即应用并保存，则调用 apply
  // applyColumnSelection();
  // 或者只是让用户在popover里看到重置效果，点确定再生效
  message.info('已重置为默认列，请点击确定应用。')
}

const carscolumns = [
  {
    title: '品牌',
    key: 'brand',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '厂家',
    key: 'manufacturer',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型分类',
    key: 'vehicle_type',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系',
    key: 'series',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型',
    key: 'model',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '年款',
    key: 'model_year',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '排量',
    key: 'displacement',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Vio',
    key: 'vio',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
]

// 标准名称与字段映射配置
const standardLabelConfigs = {
  水箱: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '芯体长（英寸）', key: '芯体长（英寸）' },
    { label: '芯体宽（英寸）', key: '芯体宽（英寸）' },
    { label: '芯体厚（英寸）', key: '芯体厚（英寸）' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '水室方向', key: '水室方向' },
    { label: 'TOC规格', key: 'TOC规格' },
    { label: 'TOC油冷器中心距mm', key: 'TOC油冷器中心距mm' },
    { label: 'EOC规格', key: 'EOC规格' },
    { label: 'EOC油冷器中心距mm', key: 'EOC油冷器中心距mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: 'AT/MT', key: 'AT/MT' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '进口管直径mm(螺纹)', key: '进口管直径mm(螺纹)' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '出口管直径mm(螺纹)', key: '出口管直径mm(螺纹)' },
    { label: '芯体排数', key: '芯体排数' },
    { label: '是否带加水口', key: '是否带加水口' },
    { label: '是否带盖子', key: '是否带盖子' },
  ],
  冷凝器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '是否含干燥瓶', key: '是否含干燥瓶' },
    { label: '干燥瓶类型', key: '干燥瓶类型' },
    { label: '是否带干燥剂', key: '是否带干燥剂' },
    { label: '是否带储液器', key: '是否带储液器' },
    { label: '储液器类型', key: '储液器类型' },
    { label: '是否双系统油散', key: '是否双系统油散' },
    { label: '双系统油散进口尺寸', key: '双系统油散进口尺寸' },
    { label: '双系统油散出口尺寸', key: '双系统油散出口尺寸' },
    { label: '制冷剂', key: '制冷剂' },
  ],
  暖风: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '是否带管子', key: '是否带管子' },
  ],
  中冷器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '冷却方式', key: '冷却方式' },
  ],
  蒸发器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
  ],
  压缩机: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '插头结构', key: '插头结构' },
    { label: '皮带轮类型', key: '皮带轮类型' },
    { label: '沟槽的数量', key: '沟槽的数量' },
    { label: '皮带盘直径mm', key: '皮带盘直径mm' },
    { label: '特定生产商', key: '特定生产商' },
    { label: '压缩机-ID', key: '压缩机-ID' },
    { label: '预充 PAG 油', key: '预充 PAG 油' },
    { label: '压缩机机油', key: '压缩机机油' },
    { label: '机油加注量ml', key: '机油加注量ml' },
    { label: '排量-压缩机CC', key: '排量-压缩机CC' },
    { label: '制冷剂', key: '制冷剂' },
    { label: '固定方式', key: '固定方式' },
    { label: '固定孔的数量', key: '固定孔的数量' },
    { label: '离合器连接器性别', key: '离合器连接器性别' },
    { label: '连接器数量', key: '连接器数量' },
    { label: '终端数量', key: '终端数量' },
    { label: '包括离合器', key: '包括离合器' },
    { label: '包含交换机', key: '包含交换机' },
    { label: '交换机服务端口', key: '交换机服务端口' },
    { label: '线圈时钟位置', key: '线圈时钟位置' },
    { label: '线圈数量', key: '线圈数量' },
    { label: '旋转方向', key: '旋转方向' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '安装孔上下间距mm', key: '安装孔上下间距mm' },
    { label: '安装孔左右间距mm', key: '安装孔左右间距mm' },
    { label: '净重（KG)', key: '净重（KG)' },
  ],
  传感器: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '插头结构', key: '插头结构' },
    { label: '插塞接头数量', key: '插塞接头数量' },
    { label: '长度mm', key: '长度mm' },
    { label: '材质', key: '材质' },
    { label: '颜色', key: '颜色' },
    { label: '传感器类型', key: '传感器类型' },
    { label: '安装位置', key: '安装位置' },
    { label: '净重（KG)', key: '净重（KG)' },
  ],
  干燥瓶: [
    { label: '干燥瓶类型', key: '干燥瓶类型' },
    { label: '是否带干燥剂', key: '是否带干燥剂' },
    { label: '是否符合OEM', key: '是否符合OEM' },
    { label: '过滤材料', key: '过滤材料' },
    { label: '保险头的材质', key: '保险头的材质' },
    { label: '直径MM', key: '直径MM' },
    { label: '长度mm', key: '长度mm' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管直径mm(螺纹)', key: '进口管直径mm(螺纹)' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管直径mm(螺纹)', key: '出口管直径mm(螺纹)' },
    { label: '连接口尺寸', key: '连接口尺寸' },
    { label: '是否有保险', key: '是否有保险' },
    { label: '是否包含垫片', key: '是否包含垫片' },
    { label: '是否带支架', key: '是否带支架' },
    { label: '是否带管子', key: '是否带管子' },
    { label: '是否有安装硬件', key: '是否有安装硬件' },
    { label: '是否有减压阀', key: '是否有减压阀' },
    { label: '是否有安装衬垫', key: '是否有安装衬垫' },
    { label: '交换机服务端口', key: '交换机服务端口' },
    { label: '终端数量', key: '终端数量' },
    { label: '是否有螺丝/钉子', key: '是否有螺丝/钉子' },
    { label: '开关口尺寸MM', key: '开关口尺寸MM' },
    { label: '是否有开关', key: '是否有开关' },
    { label: '开关数量', key: '开关数量' },
    { label: '是否有观察的玻璃镜', key: '是否有观察的玻璃镜' },
    { label: '是否有玻璃镜', key: '是否有玻璃镜' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '铝制插头规格', key: '铝制插头规格' },
    { label: '塑料插头规格', key: '塑料插头规格' },
    { label: '塑料网规格', key: '塑料网规格' },
    { label: '塑料头规格', key: '塑料头规格' },
    { label: '净重（KG)', key: '净重（KG)' },
  ],
  膨胀阀: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '材质', key: '材质' },
    { label: '压力(bar)', key: '压力(bar)' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '膨胀阀类型', key: '膨胀阀类型' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '进口管直径(英寸)', key: '进口管直径(英寸)' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '出口管直径(英寸)', key: '出口管直径(英寸)' },
    { label: '吸入口类型', key: '吸入口类型' },
    { label: '进口结构', key: '进口结构' },
    { label: '进气口直径mm', key: '进气口直径mm' },
    { label: '进气口直径(英寸)', key: '进气口直径(英寸)' },
    { label: '出口结构', key: '出口结构' },
    { label: '岀气口直径(mm)', key: '岀气口直径(mm)' },
    { label: '岀气口直径(英寸)', key: '岀气口直径(英寸)' },
    { label: '毛细管长度(mm)', key: '毛细管长度(mm)' },
    { label: '毛细管温包长度(mm)', key: '毛细管温包长度(mm)' },
    { label: '外平衡管毛细管长度(mm)', key: '外平衡管毛细管长度(mm)' },
    { label: '外平衡管尺寸(mm)', key: '外平衡管尺寸(mm)' },
    { label: '外平衡管尺寸(英寸)', key: '外平衡管尺寸(英寸)' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
  ],
  电子扇: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '额定功率【W】', key: '额定功率【W】' },
    { label: '直径MM', key: '直径MM' },
    { label: '插头结构', key: '插头结构' },
    { label: '插塞接头数量', key: '插塞接头数量' },
    { label: '通风机叶片的数量', key: '通风机叶片的数量' },
    { label: '是否带控制模块', key: '是否带控制模块' },
    { label: '净重（KG)', key: '净重（KG)' },
  ],
  default: [],
}

const showModal = ref(false)
const currentStandardLabel = ref('')
const productDetailData = ref({})
const carsdata = ref([])
const allviocount = ref(0)
const currentRowData = ref({}) // 存储当前行数据用于产品属性详情
const currentTabValue = ref('basic') // 当前选中的tab

// 新增四个接口的数据存储
const referenceInfoData = ref([])
const replacementInfoData = ref([])
const priceCategoryInfoData = ref([])
const supplyListData = ref([])

// 加载状态
const referenceInfoLoading = ref(false)
const replacementInfoLoading = ref(false)
const priceCategoryInfoLoading = ref(false)
const supplyListLoading = ref(false)
const showComAttrModal = ref(false)
const comAttrData = ref({
  customer_code: null,
  packaging_code: null,
  kingdee_sync: null,
})

const rowKey = (row) => row.id
// 选中的行 keys
const checkedRowKeys = ref([])

const handleCheck = (keys) => {
  console.log('Checked keys changed:', keys)
  // checkedRowKeys.value = keys; // v-model 会自动更新
}

const handleBatchOperation = () => {
  if (checkedRowKeys.value.length === 0) {
    message.warning('请至少选择一项进行操作！')
    return
  }
  showComAttrModal.value = true
}

const handleEditClick = async (rowData) => {
  showComAttrModal.value = true
  checkedRowKeys.value.push(rowData.id)
}

const handleComSubmit = async () => {
  const id_info = { id_list: checkedRowKeys.value }
  const post_data = { ...comAttrData.value, ...id_info }
  await api.AddCommodityAttr(post_data)
  checkedRowKeys.value = []
  showComAttrModal.value = false
}

const Detail = async (rowData) => {
  try {
    // 存储当前行数据
    currentRowData.value = rowData

    // if (!rowData.MaterielCode || rowData.MaterielCode === '') {
    //   productDetailData.value = {}
    // } else {
    //   const prod_res = await api.getProductDetail({
    //     group_number: rowData.MaterielCode,
    //   })
    //   productDetailData.value = prod_res.data
    // }
    const prod_res = await api.getProductDetail({
      group_number: rowData.MaterialCode,
    })
    productDetailData.value = prod_res.data

    // 调试信息：打印产品详情数据
    console.log('产品详情数据:', prod_res.data)
    console.log('简图URL:', prod_res.data?.diagram)
    console.log('实物图列表:', prod_res.data?.photo_list)

    const cars_res = await api.searchCarsByProduct({
      product: rowData.MaterialCode,
    })
    carsdata.value = cars_res.data
    allviocount.value = carsdata.value.reduce((acc, item) => acc + (Number(item.vio) || 0), 0)

    currentStandardLabel.value = prod_res.data.part_name

    // 调用四个新接口获取数据
    await loadTabData(rowData.MaterialCode)

    // 设置默认选中基础信息tab
    currentTabValue.value = 'basic'

    showModal.value = true
  } catch (error) {
    message.error('获取OE详情失败')
  }
}

// 加载选项卡数据的函数
const loadTabData = async (reachCode) => {
  const params = { reach_code: reachCode }

  // 并行调用四个接口
  const promises = [
    loadReferenceInfo(params),
    loadReplacementInfo(params),
    loadPriceCategoryInfo(params),
    loadSupplyList(params),
  ]

  await Promise.allSettled(promises)
}

// 处理物料代码点击事件
const handleMaterialCodeClick = async (materialCode) => {
  try {
    // 使用getProductInfoByCode接口获取基础信息
    const basicInfoRes = await api.getProductInfoByCode({
      reach_code: materialCode,
    })

    if (basicInfoRes.data) {
      // 更新基础信息数据
      productDetailData.value = basicInfoRes.data

      // 更新当前行数据，使用新的物料代码
      currentRowData.value = {
        ...currentRowData.value,
        MaterialCode: materialCode,
      }

      // 获取车型详情数据
      const cars_res = await api.searchCarsByProduct({
        product: materialCode,
      })
      carsdata.value = cars_res.data
      allviocount.value = carsdata.value.reduce((acc, item) => acc + (Number(item.vio) || 0), 0)

      // 刷新所有tab的数据
      await loadTabData(materialCode)

      // 自动跳转到基础信息页面
      currentTabValue.value = 'basic'

      message.success('产品信息已更新')
    } else {
      message.warning('未找到该物料代码的产品信息')
    }
  } catch (error) {
    console.error('获取物料代码产品信息失败:', error)
    message.error('获取产品信息失败')
  }
}

// 加载参考信息
const loadReferenceInfo = async (params) => {
  try {
    referenceInfoLoading.value = true
    const res = await api.getReferenceInfo(params)
    referenceInfoData.value = res.data || []
  } catch (error) {
    console.error('获取参考信息失败:', error)
    referenceInfoData.value = []
  } finally {
    referenceInfoLoading.value = false
  }
}

// 加载替代说明
const loadReplacementInfo = async (params) => {
  try {
    replacementInfoLoading.value = true
    const res = await api.getReplacementInfo(params)
    replacementInfoData.value = res.data || []
  } catch (error) {
    console.error('获取替代说明失败:', error)
    replacementInfoData.value = []
  } finally {
    replacementInfoLoading.value = false
  }
}

// 加载Reach-供应商对照
const loadPriceCategoryInfo = async (params) => {
  try {
    priceCategoryInfoLoading.value = true
    const res = await api.getPriceCategoryInfo(params)
    priceCategoryInfoData.value = res.data || []
  } catch (error) {
    console.error('获取Reach-供应商对照失败:', error)
    priceCategoryInfoData.value = []
  } finally {
    priceCategoryInfoLoading.value = false
  }
}

// 加载供应商物流信息
const loadSupplyList = async (params) => {
  try {
    supplyListLoading.value = true
    const res = await api.getSupplyList(params)
    supplyListData.value = res.data || []
  } catch (error) {
    console.error('获取供应商物流信息失败:', error)
    supplyListData.value = []
  } finally {
    supplyListLoading.value = false
  }
}

// 计算当前配置
const currentConfig = computed(() => {
  return (
    standardLabelConfigs[productDetailData.value?.part_name] || standardLabelConfigs.default || []
  )
})

// 组装两列显示（每行两个字段）
const detailRows = computed(() => {
  const data = productDetailData.value || {}
  const fields = currentConfig.value
  const rows = []
  for (let i = 0; i < fields.length; i += 2) {
    const f1 = fields[i]
    const f2 = fields[i + 1]
    rows.push({
      label: f1?.label || '',
      value: data[f1?.key] ?? '-',
      label2: f2?.label || '',
      value2: f2 ? data[f2.key] ?? '-' : '',
      // 可根据 key 或 label 做高亮判断
      labelClass: f1?.highlight,
      valueClass: f1?.highlight,
      label2Class: f2?.highlight,
      value2Class: f2?.highlight,
    })
  }
  return rows
})

// 图片相关计算属性
const productPhotoList = computed(() => productDetailData.value?.photo_list || [])
const productDiagram = computed(() => productDetailData.value?.diagram || '')

// 图片预览相关状态
const showImagePreview = ref(false)
const previewImageUrl = ref('')
const currentCarouselIndex = ref(0)
const previewImageList = ref([])
const currentPreviewIndex = ref(0)
const imageScale = ref(1)
const imagePosition = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })

// 图片点击放大功能
const handleImageClick = (imageUrl, imageList = null, index = 0) => {
  previewImageUrl.value = imageUrl
  previewImageList.value = imageList || [imageUrl]
  currentPreviewIndex.value = index
  imageScale.value = 1
  imagePosition.value = { x: 0, y: 0 }
  showImagePreview.value = true
  // 添加键盘监听器
  nextTick(() => {
    document.addEventListener('keydown', handleKeyDown)
  })
}

// 轮播图图片点击处理
const handleCarouselImageClick = (imageUrl, index) => {
  handleImageClick(imageUrl, productPhotoList.value, index)
}

// 预览图片切换
const switchPreviewImage = (direction) => {
  if (previewImageList.value.length <= 1) return

  if (direction === 'prev') {
    currentPreviewIndex.value =
      currentPreviewIndex.value > 0
        ? currentPreviewIndex.value - 1
        : previewImageList.value.length - 1
  } else {
    currentPreviewIndex.value =
      currentPreviewIndex.value < previewImageList.value.length - 1
        ? currentPreviewIndex.value + 1
        : 0
  }

  previewImageUrl.value = previewImageList.value[currentPreviewIndex.value]
  // 切换图片时重置缩放和位置
  imageScale.value = 1
  imagePosition.value = { x: 0, y: 0 }
}

// 图片缩放
const handleImageWheel = (event) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.max(0.5, Math.min(3, imageScale.value + delta))
  imageScale.value = newScale

  // 如果缩放到1，重置位置
  if (newScale === 1) {
    imagePosition.value = { x: 0, y: 0 }
  }
}

// 图片拖拽开始
const handleImageMouseDown = (event) => {
  if (imageScale.value <= 1) return
  isDragging.value = true
  dragStart.value = {
    x: event.clientX - imagePosition.value.x,
    y: event.clientY - imagePosition.value.y,
  }
  event.preventDefault()
}

// 图片拖拽移动
const handleImageMouseMove = (event) => {
  if (!isDragging.value || imageScale.value <= 1) return
  imagePosition.value = {
    x: event.clientX - dragStart.value.x,
    y: event.clientY - dragStart.value.y,
  }
}

// 图片拖拽结束
const handleImageMouseUp = () => {
  isDragging.value = false
}

// 重置图片状态
const resetImageState = () => {
  imageScale.value = 1
  imagePosition.value = { x: 0, y: 0 }
  isDragging.value = false
}

// 关闭预览时重置状态
const closeImagePreview = () => {
  showImagePreview.value = false
  resetImageState()
  document.removeEventListener('keydown', handleKeyDown)
}

// 键盘事件处理
const handleKeyDown = (event) => {
  if (!showImagePreview.value) return

  switch (event.key) {
    case 'Escape':
      closeImagePreview()
      break
    case 'ArrowLeft':
      event.preventDefault()
      switchPreviewImage('prev')
      break
    case 'ArrowRight':
      event.preventDefault()
      switchPreviewImage('next')
      break
    case '=':
    case '+':
      event.preventDefault()
      imageScale.value = Math.min(3, imageScale.value + 0.2)
      break
    case '-':
      event.preventDefault()
      imageScale.value = Math.max(0.5, imageScale.value - 0.2)
      break
    case '0':
      event.preventDefault()
      resetImageState()
      break
  }
}

// 车型表格行属性
const rowProps = () => ({
  style: 'cursor: pointer;',
})

// 生成动态表格列的函数
const generateDynamicColumns = (data) => {
  if (!data || data.length === 0) return []

  const firstItem = data[0]
  return Object.keys(firstItem).map((key) => ({
    title: key,
    key: key,
    ellipsis: { tooltip: true },
    width: 120,
    render: (row) => row[key] || '-',
  }))
}

// 各个选项卡的表格列
const getReferenceInfoColumns = () => generateDynamicColumns(referenceInfoData.value)
const getReplacementInfoColumns = () => {
  if (!replacementInfoData.value || replacementInfoData.value.length === 0) return []

  const firstItem = replacementInfoData.value[0]
  return Object.keys(firstItem).map((key) => {
    // 如果是物料代码字段，且替代说明数据不为空，则渲染为可点击的蓝色链接
    if (key === '物料代码' && replacementInfoData.value.length > 0) {
      return {
        title: key,
        key: key,
        ellipsis: { tooltip: true },
        width: 120,
        render: (row) => {
          const materialCode = row[key]
          if (!materialCode || materialCode === '-') {
            return materialCode || '-'
          }
          return h(
            'a',
            {
              class: 'text-blue-500 hover:text-blue-700 cursor-pointer underline',
              onClick: () => handleMaterialCodeClick(materialCode),
            },
            materialCode
          )
        },
      }
    }
    // 其他字段保持原样
    return {
      title: key,
      key: key,
      ellipsis: { tooltip: true },
      width: 120,
      render: (row) => row[key] || '-',
    }
  })
}
const getPriceCategoryInfoColumns = () => generateDynamicColumns(priceCategoryInfoData.value)
const getSupplyListColumns = () => generateDynamicColumns(supplyListData.value)

// 产品属性详情行数据（使用rowData的所有字段）
const productAttributeRows = computed(() => {
  const data = currentRowData.value || {}
  const rows = []

  // 获取所有字段，排除actions、id等非展示字段
  const excludeKeys = ['actions', 'id', 'ID', 'Id']
  const keys = Object.keys(data).filter((key) => !excludeKeys.includes(key))

  // 找到对应的列标题
  const getColumnTitle = (key) => {
    const column = allColumns.find((col) => col.key === key)
    return column ? column.title : key
  }

  // 两列显示
  for (let i = 0; i < keys.length; i += 2) {
    const key1 = keys[i]
    const key2 = keys[i + 1]

    rows.push({
      label: getColumnTitle(key1),
      value: data[key1] ?? '-',
      label2: key2 ? getColumnTitle(key2) : '',
      value2: key2 ? data[key2] ?? '-' : '',
    })
  }

  return rows
})

// =============================================================================================================

// 添加验证状态
const formRef = ref(null)
const basicRules = {
  category: {
    required: true,
    trigger: ['change'],
    message: '请选择品类',
  },
  market: {
    required: true,
    trigger: 'change',
    message: '请输入市场',
  },
  suffix: {
    required: true,
    trigger: 'change',
    message: '请输入尾缀',
  },
}

const onlyAllowNumberLetter = (value) => !value || /^[0-9A-Za-z]+$/.test(value)

// 添加失去焦点验证
// const handleBlur = (field) => {
//   formRef.value?.validate([field])
// }

// 控制车型选择弹窗显示
const showVehicleSelector = ref(false)
// 控制OE选择弹窗显示
const showOESelector = ref(false)
// 新增状态 - 产品创建
const showCreateModal = ref(false)
const currentStep = ref(1)
const formData = ref({
  basic: {
    category: null,
    market: null,
    suffix: null,
  },
  reference: {
    oes: [], // 存放选中的oe号
    vehicles: [], // 存放选中的reach_car_id
    competitors: [], // 存放添加的竞品品牌信息{"竞品名":"竞品号"}
  },
  logistics: [],
  attachments: [],
  paramsInfo: {},
  availableInfo: {
    available: 'available',
    remark: null,
  },
})
const market = ref(['中国', '北美', '欧洲', '俄罗斯', '加拿大', '墨西哥', '东南亚'])
const logisticsList = ref([
  {
    supplier: '',
    code: '',
    price: null,
    innerLength: null,
    innerWidth: null,
    innerHeight: null,
    netWeight: null,
    grossWeight: null,
    mpq: null,
    moq: null,
    boxLength: null,
    boxWidth: null,
    boxHeight: null,
    boxGrossWeight: null,
  },
])

// 新增方法 - 产品创建
const openCreate = () => {
  showCreateModal.value = true
  currentStep.value = 1
  formData.value = {
    basic: {
      category: null,
      market: null,
      suffix: null,
    },
    reference: {
      oes: [],
      vehicles: [],
      competitors: [],
    },
    params: {},
    attachments: [],
    availableInfo: {
      available: 'available',
      remark: null,
    },
  }
  logisticsList.value = [
    {
      supplier: '',
      code: '',
      price: null,
      innerLength: null,
      innerWidth: null,
      innerHeight: null,
      netWeight: null,
      grossWeight: null,
      mpq: null,
      moq: null,
      boxLength: null,
      boxWidth: null,
      boxHeight: null,
      boxGrossWeight: null,
    },
  ]
}

// 新增供应商方法
const addSupplier = () => {
  logisticsList.value.push({
    supplier: '',
    code: '',
    price: null,
    innerLength: null,
    innerWidth: null,
    innerHeight: null,
    netWeight: null,
    grossWeight: null,
    mpq: null,
    moq: null,
    boxLength: null,
    boxWidth: null,
    boxHeight: null,
    boxGrossWeight: null,
  })
}

const removeSupplier = (index) => {
  if (logisticsList.value.length > 1) {
    logisticsList.value.splice(index, 1)
  }
}

const handleNext = async () => {
  // 第一步验证
  if (currentStep.value === 1) {
    try {
      console.log('第一步完成，点击下一步的品类信息为：', formData.value.basic)
      await formRef.value?.validate()
    } catch (errors) {
      message.error('请先完善基础信息')
      return
    }
  }

  // 第二步验证产品是否存在，并且将选择的OE和车型reach_car_id传入formData中
  if (currentStep.value === 2) {
    try {
      // 点击下一步，先将OE和车型的reach_car_id推入formData中
      formData.value.reference.oes = selectedOEs.value.map((oe) => oe.oe_num)
      formData.value.reference.vehicles = selectedVehicles.value.map(
        (vehicle) => vehicle.reach_car_id
      )
      console.log('------->第二步完成，点击下一步的oe和车型为', formData.value.reference)
      const res = await api.checkProductExist(formData.value.reference)
      if (res.data?.length > 0) {
        existingProducts.value = res.data
        showExistModal.value = true
        return
      }
    } catch (error) {
      message.error('检查产品失败')
      return
    }
  }

  if (currentStep.value === 3) {
    console.log('------------>logisticsList的数据为', logisticsList.value)
    formData.value.logistics = logisticsList.value
    console.log('------------>第三步完成：点击下一步的供应商信息为：', formData.value.logistics)
  }

  if (currentStep.value === 4) {
    console.log('------------>产品参数params为：', productParams)
    formData.value.paramsInfo = productParams
    console.log('------------>第四步完成：点击下一步的产品参数为：', formData.value.paramsInfo)
  }

  if (currentStep.value === 5) {
    console.log('------------>第五步完成：点击下一步的产品附件为：', formData.value.attachments)
  }

  if (currentStep.value === 6) {
    console.log('------------>第六步完成：点击下一步的产品附件为：', formData.value.availableInfo)
  }

  if (currentStep.value < 6) currentStep.value++
}

const handlePrev = () => {
  if (currentStep.value > 1) currentStep.value--
}

const lastBrandRef = ref(null)
// 添加竞品信息
const addCompetitorInfo = async () => {
  console.log('------------->添加竞品信息')
  formData.value.reference.competitors.push({ CompetitorBrand: '', Cem: '' })
  await nextTick()
  if (lastBrandRef.value) {
    lastBrandRef.value.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
    })
  }
}

const article_brand_type = ref(['4SEASONS', 'AVA', 'DPI', 'Dorman', 'NISSENS', 'NRF', 'UAC'])

const removeBrand = (index) => {
  formData.value.reference.competitors.splice(index, 1)
}

const submitProduct = async () => {
  try {
    console.log('--------->产品创建提交form数据', formData.value)
    await api.createProduct({
      basic: formData.value.basic,
      reference: formData.value.reference,
      logistics: [...formData.value.logistics],
      paramsInfo: formData.value.paramsInfo,
      attachments: formData.value.attachments,
    })
    message.success('创建成功')
    showCreateModal.value = false
  } catch (error) {
    message.error('创建失败')
  }
}

const showExistModal = ref(false)
const existingProducts = ref([])

const showDetail = (row) => {
  Detail(row)
  showExistModal.value = false
}

// 新增存在产品表格列配置
const existColumns = [
  {
    title: '物料编码',
    key: 'Materiel_Number',
    render(row) {
      return h(
        'a',
        {
          class: 'text-blue-500 hover:cursor-pointer',
          onClick: () => showDetail(row),
        },
        row.Materiel_Number
      )
    },
  },
  {
    title: '零件名称',
    key: 'part_name',
  },
]

// 新增产品参数状态
const productParams = ref({})

// 计算参数配置
const productParamConfig = computed(() => {
  return standardLabelConfigs[formData.value.basic.category] || []
})

//批量添加产品操作
// Modal State
const showBatchImportModal = ref(false)
const BatchImportCurrentStep = ref(1)
const BatchImportLoading = ref(false)
const importToDbLoading = ref(false) // 单独的入库loading状态
const BatchImportStepStatus = computed(() => (BatchImportLoading.value ? 'process' : 'finish'))

const modalTitle = computed(() => {
  switch (BatchImportCurrentStep.value) {
    case 1:
      return '步骤 1: 上传文件与备注'
    case 2:
      return '步骤 2: 预览并确认'
    case 3:
      return '步骤 3: 完成入库'
    default:
      return '批量导入'
  }
})

// --- Step 1 State ---
const remarks = ref('')
const fileList = ref([])
const selectedFile = computed(() => fileList.value[0]?.file || null)

// --- Step 2 State (Preview Table) ---
const previewTable = ref({
  columns: [],
  data: [],
})

// --- Step 3 State (Final Table) ---
const finalTable = ref({
  columns: [],
  data: [],
})

// --- Functions ---

const openImportModal = () => {
  resetAllState() // Ensure clean state when opening
  showBatchImportModal.value = true
  BatchImportCurrentStep.value = 1
  showCreateModal.value = false
}

const closeModal = () => {
  showBatchImportModal.value = false
  // resetAllState will be called by @after-leave
}

const resetAllState = () => {
  BatchImportCurrentStep.value = 1
  remarks.value = ''
  fileList.value = []
  previewTable.value = { columns: [], data: [] }
  finalTable.value = { columns: [], data: [] }
  BatchImportLoading.value = false
  importToDbLoading.value = false
}

// Step 1 Actions
const downloadTemplate = async () => {
  BatchImportLoading.value = true
  message.info(`正在下载模板...`)

  try {
    const minioUrl = 'http://192.168.5.235:9000/products/物料申请表_模板.xlsx'
    const filename = '物料申请表_模板.xlsx'

    // 创建一个隐藏的链接元素直接下载
    const link = document.createElement('a')
    link.href = minioUrl
    link.download = filename
    link.target = '_blank' // 在新标签页打开，避免跨域问题

    // 添加到DOM，点击，然后移除
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success(`模板下载成功!`)
  } catch (error) {
    console.error('下载失败:', error)
    message.error(error.message || '下载失败，请重试')
  } finally {
    BatchImportLoading.value = false
  }
}

// Step 1 Actions - 上传文件并获取预览数据
const handleFileChange = (options) => {
  // fileList is automatically updated by v-model
  if (options.fileList.length > 0) {
    message.success(`已选择文件: ${options.fileList[0].name}`)
  } else {
    message.info('已清除选择的文件')
  }
}

// 辅助函数：计算列宽
const calculateColumnWidth = (title) => {
  if (!title) return 120

  // 计算中文字符数量
  const chineseCharCount = (title.match(/[\u4e00-\u9fa5]/g) || []).length
  const englishCharCount = title.length - chineseCharCount

  // 中文字符约16px，英文字符约9px
  const estimatedWidth = chineseCharCount * 16 + englishCharCount * 9

  // 最小80px，最大250px，加上padding
  return Math.max(80, Math.min(250, estimatedWidth + 30))
}

// 处理列配置，添加动态宽度
const processColumnsWithDynamicWidth = (columns) => {
  return columns.map((col) => ({
    ...col,
    width: calculateColumnWidth(col.title || col.key),
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
  }))
}

const uploadAndPreview = async () => {
  if (!selectedFile.value) {
    message.warning('请选择要导入的文件')
    return
  }
  BatchImportLoading.value = true
  // message.loading('正在解析文件并生成预览数据，请稍候...');

  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('remarks', remarks.value)

    const response = await api.uploadFileForPreview(formData)

    const processedColumns = processColumnsWithDynamicWidth(response.table_columns)

    previewTable.value.columns = processedColumns
    previewTable.value.data = response.data

    BatchImportCurrentStep.value = 2
    message.success('文件解析完成，请确认预览数据!')
  } catch (error) {
    console.error('文件上传失败:', error)
    message.error(error.message || '文件处理失败，请重试')
  } finally {
    BatchImportLoading.value = false
  }
}

// Step 2 Actions - 确定生成最终数据
const confirmGeneration = async () => {
  BatchImportLoading.value = true
  // message.loading('正在生成最终数据，请稍候...');

  try {
    const processedData = previewTable.value.data.map((item) => {
      const processedItem = {
        ...item,
      }
      return processedItem
    })

    const response = await api.generateFinalData(processedData)

    // 设置最终表格数据 (实际应该来自后端响应)
    finalTable.value.columns = previewTable.value.columns.filter(
      (column) => !['part_verify_desc', 'part_status'].includes(column.key)
    )

    finalTable.value.data = response.data

    BatchImportCurrentStep.value = 3
    message.success('最终数据生成完成，请确认后点击完成入库!')
  } catch (error) {
    console.error('数据生成失败:', error)
    message.error(error.message || '数据生成失败，请重试')
  } finally {
    BatchImportLoading.value = false
  }
}

// Step 3 Actions - 真正的入库操作
const completeImportToDatabase = async () => {
  importToDbLoading.value = true
  message.loading('正在执行入库操作，请稍候...')

  try {
    const processedData = finalTable.value.data.map((item) => {
      const processedItem = {
        ...item,
      }
      return processedItem
    })

    await api.importToDatabase({
      finalData: processedData,
      remarks: remarks.value,
    })

    message.success('批量导入入库完成！数据已成功保存, 等待审核。')

    closeModal()
  } catch (error) {
    console.error('入库操作失败:', error)
    message.error(error.message || '入库操作失败，请重试')
  } finally {
    importToDbLoading.value = false
  }
}

// Generic Export Function
const exportResults = (data, filenamePrefix) => {
  if (!data || data.length === 0) {
    message.warning('没有数据可以导出')
    return
  }

  BatchImportLoading.value = true
  message.info('正在准备导出数据...')

  // Simulate some processing if needed
  setTimeout(() => {
    try {
      const columns =
        BatchImportCurrentStep.value === 2 ? previewTable.value.columns : finalTable.value.columns

      // 准备Excel数据
      const excelData = data.map((row) => {
        const rowData = {}
        columns.forEach((col) => {
          const header = col.title || col.key
          rowData[header] = row[col.key] || ''
        })
        return rowData
      })

      // 创建工作簿和工作表
      const worksheet = XLSX.utils.json_to_sheet(excelData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      // 设置列宽（可选）
      const colWidths = columns.map((col) => ({
        wch: Math.max(10, (col.title || col.key).length + 2),
      }))
      worksheet['!cols'] = colWidths

      // 生成Excel文件并下载
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const filename = `${filenamePrefix}_${timestamp}.xlsx`

      XLSX.writeFile(workbook, filename)
      message.success('数据导出成功!')
    } catch (error) {
      console.error('Export error:', error)
      message.error('导出失败，请查看控制台')
    } finally {
      BatchImportLoading.value = false
    }
  }, 500)
}
// 将获取的车型数据转成Raw
const plainSelectedVehicles = computed(() => {
  return toRaw(selectedVehicles.value)
})

// 删除OE及其关联车型
const removeOeAndAssociatedVehicles = (oeNum) => {
  // 1. 找到要删除的 OE
  const oeToRemove = selectedOEs.value.find((oe) => oe.oe_num === oeNum)
  if (!oeToRemove) return

  // 2. 提取该 OE 所有关联的 reach_car_id
  const carIdsToRemove = oeToRemove.vehicle_info.map((v) => v.reach_car_id)

  // 3. 从 selectedOEs 中删除该 OE
  selectedOEs.value = selectedOEs.value.filter((oe) => oe.oe_num !== oeNum)

  // 4. 从 selectedVehicles 中删除所有关联的车型
  selectedVehicles.value = selectedVehicles.value.filter(
    (vehicle) => !carIdsToRemove.includes(vehicle.reach_car_id)
  )

  // 可选提示
  window.$message?.success?.(`OE ${oeNum} 及其 ${carIdsToRemove.length} 个关联车型已删除`)
}

const removeMainNumAndAssociatedVehicles = (mainNum) => {
  // 1. 找到要删除的主号并删除
  selectedMainNum.value = selectedMainNum.value.filter(
    (mainNumObj) => mainNumObj.Number !== mainNum
  )
  // 2. 删除主号相关的车型
  selectedVehicles.value = selectedVehicles.value.filter(
    (item) => !carIdsFromBindOeSelector.value.includes(item.reach_car_id)
  )
}

// 删除OE及其关联车型
const removeVehicles = (vehicleId) => {
  // 1. 找到要删除的 OE
  selectedVehicles.value = selectedVehicles.value.filter(
    (vehicle) => vehicle.reach_car_id !== vehicleId
  )
}

/* ------供应商------*/
// 存放供应商映射 { 名称: 编码 }
const supplierMap = ref({})

// 选择的供应商名称
const selectSupplierName = ref('')

// 从后端获取供应商数据
const loadSupplierData = async () => {
  const response = await api.getSupplierMap()

  const map = {}
  response.data.forEach((item) => {
    const keys = Object.keys(item)
    const nameField = keys[1] // 供应商名称字段
    const codeField = keys[0] // 供应商编码字段
    map[item[nameField]] = item[codeField]
  })

  supplierMap.value = map // ✅ 赋值给 ref
}

// 计算当前选中的供应商代码
const selectedCode = computed(() => {
  return supplierMap.value[selectSupplierName.value] || ''
})

// 构造 n-select 所需的 options 格式
const supplierOptions = computed(() => {
  return Object.keys(supplierMap.value).map((name) => ({
    label: name,
    value: name,
  }))
})

// 当前 item 选择供应商时，自动填充其 supplierCode
const handleSupplierChange = (item, supplierName) => {
  item.code = supplierMap.value[supplierName] || ''
}

/* ----上传附件---- */
const uploadedFile = ref(null) // 存储成功后的 fileKey
const downloadUrl = ref('')

// 自定义上传逻辑
const customUpload = async ({ file, onFinish, onError, onProgress }) => {
  const filename = file.name
  const contentType = file.file.type || 'application/octet-stream'

  try {
    // 1. 向后端请求 MinIO 签名上传链接
    const res = await api.minIOUploadAttachments({ filename, contentType })
    const data = res.data
    const { uploadUrl, fileKey } = data

    // 2. 直接上传到 MinIO（不经过你的服务器！）
    const uploadRes = await fetch(uploadUrl, {
      method: 'PUT',
      body: file.file, // 原始 File 对象
      headers: {
        'Content-Type': contentType,
      },
    })

    if (uploadRes.ok) {
      message.success('上传成功！')
      onFinish()
      // 3. 保存 fileKey，用于生成下载链接
      uploadedFile.value = fileKey
      // 把文件路径记录下来
      formData.value.attachments.push(fileKey)
      console.log('****上传文件，form表单数据为***>', formData)
      // 生成下载链接（可以缓存，或从后端获取）
      // downloadUrl.value = `http://127.0.0.1:3100/api/manage/products/minio/products/download/${encodeURIComponent(fileKey)}`
    } else {
      message.error('上传失败')
      onError()
    }
  } catch (err) {
    message.error('上传失败: ' + err.message)
    onError()
  }
}
</script>

<template>
  <!-- 引入车型选择弹窗 -->
  <CarModelSelector
    :show="showVehicleSelector"
    :default-selected-ids="selectedVehicles"
    @update:show="showVehicleSelector = $event"
    @select="handleCarModelSelect"
  />
  <!-- 引入OE选择弹窗 -->
  <OESelector
    :show="showOESelector"
    :category="formData.basic.category"
    :select-car-ids-from-index="selectedVehicles"
    @update:show="showOESelector = $event"
    @select="handleOESelectMainNum"
  />

  <CommonPage>
    <n-grid :cols="20" :x-gap="12" class="mb-4">
      <n-gi :span="4">
        <n-form-item label="物料代码" :label-width="80" label-placement="left">
          <n-input
            v-model:value="queryItems.MaterialCode"
            placeholder="物料代码"
            clearable
            @update:value="(v) => handleOptionChange('MaterialCode', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="零件名称" :label-width="80" label-placement="left">
          <n-select
            v-model:value="queryItems.ChineseName"
            :options="ChineseName_options"
            placeholder="零件名称"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('ChineseName', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="属性" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.Attributes"
            :options="Attributes_options"
            placeholder="属性"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('Attributes', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="可供状态" :label-width="80" label-placement="left">
          <n-select
            v-model:value="queryItems.Availability"
            :options="Availability_options"
            placeholder="可供状态"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('Availability', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button class="ml-2" @click="customReset">重置</n-button>
      </n-gi>
    </n-grid>
    <div class="dynamic-table-container">
      <div class="table-toolbar">
        <n-space>
          <n-button type="primary" @click="openCreate">新建产品</n-button>
          <n-button type="primary" @click="handleBatchOperation">批量操作</n-button>
          <n-popover
            ref="columnSelectorPopoverRef"
            trigger="click"
            placement="bottom-end"
            style="padding: 0"
            :on-update:show="handlePopoverUpdateShow"
          >
            <template #trigger>
              <n-button :icon="SettingsIcon">选择列</n-button>
            </template>
            <div class="column-selector-panel">
              <div
                style="
                  padding: 12px 12px 0 12px;
                  font-weight: bold;
                  border-bottom: 1px solid #eee;
                  margin-bottom: 8px;
                "
              >
                自定义显示列
              </div>
              <n-scrollbar style="max-height: 300px; padding: 0 12px">
                <n-checkbox-group v-model:value="tempVisibleColumnKeys">
                  <div v-for="column in allColumns" :key="column.key" class="column-selector-item">
                    <n-checkbox
                      :value="column.key"
                      :label="column.title"
                      :disabled="
                        tempVisibleColumnKeys.length === 1 &&
                        tempVisibleColumnKeys[0] === column.key
                      "
                    />
                  </div>
                </n-checkbox-group>
              </n-scrollbar>
              <div class="column-selector-actions">
                <n-button size="small" @click="resetToDefaultColumns">重置</n-button>
                <div>
                  <n-button size="small" style="margin-right: 8px" @click="cancelColumnSelection"
                    >取消</n-button
                  >
                  <n-button size="small" type="primary" @click="applyColumnSelection"
                    >确定</n-button
                  >
                </div>
              </div>
            </div>
          </n-popover>
        </n-space>
      </div>

      <n-data-table
        v-model:checked-row-keys="checkedRowKeys"
        :columns="displayColumns"
        :data="tableData"
        :bordered="true"
        :single-line="false"
        flex-height
        style="height: 480px"
        :pagination="pagination"
        :remote="true"
        :row-key="rowKey"
        @update:page="handleUpdatePage"
        @update:page-size="handleUpdatePageSize"
        @update:checked-row-keys="handleCheck"
      />
    </div>

    <n-modal v-model:show="showComAttrModal" preset="dialog" :style="{ width: '700px' }">
      <n-card title="添加商品数据" content-style="padding: 16px; max-height: 70vh;">
        <div>
          <n-form ref="formRef" :model="comAttrData" label-placement="left" label-width="120">
            <n-form-item label="客户代码" path="customer_code" required>
              <n-input v-model:value="comAttrData.customer_code" />
            </n-form-item>
            <n-form-item label="包装代码" path="packaging_code" required>
              <n-input v-model:value="comAttrData.packaging_code" />
            </n-form-item>
            <n-form-item label="同步金蝶" path="kingdee_sync" required>
              <n-input v-model:value="comAttrData.kingdee_sync" />
            </n-form-item>
          </n-form>
        </div>
        <template #footer>
          <div class="flex justify-end gap-4">
            <n-button @click="showComAttrModal = false">取消</n-button>
            <n-button type="primary" @click="handleComSubmit"> 确定 </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showExistModal" :style="{ width: '600px' }">
      <n-card title="系统已存在相同产品" content-style="padding: 16px;">
        <n-data-table :columns="existColumns" :data="existingProducts" :bordered="false" />
        <template #footer>
          <n-button type="primary" @click="showExistModal = false">关闭</n-button>
        </template>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showCreateModal" preset="dialog" :style="{ width: '850px' }">
      <n-card title="新建产品" content-style="padding: 16px; max-height: 70vh;">
        <n-steps :current="currentStep" style="margin-bottom: 24px">
          <n-step title="基础信息" description="填写产品品类、市场、尾缀" />
          <n-step title="参考信息" description="填写OE、适用车型" />
          <n-step title="物流信息" description="添加供应商及物流详情" />
          <n-step title="产品参数" description="填写产品具体规格参数" />
          <n-step title="附件" description="填写产品附件" />
          <n-step title="备注" description="填写产品可供备注" />
        </n-steps>

        <!-- 步骤内容 -->
        <div v-show="currentStep === 1">
          <n-form
            ref="formRef"
            :model="formData.basic"
            :rules="basicRules"
            label-placement="left"
            label-width="auto"
          >
            <n-form-item label="品类" path="category" required>
              <n-select
                id="productType"
                v-model:value="formData.basic.category"
                :options="productTypeOptions"
                placeholder="请选择产品类型"
                filterable
                clearable
                @update:value='handleCategoryChange'
              />
            </n-form-item>
            <n-form-item label="市场" path="market" required>
              <n-select
                v-model:value="formData.basic.market"
                :options="market.map((c) => ({ label: c, value: c }))"
                filterable
                clearable
              />
            </n-form-item>
            <n-form-item label="尾缀" path="suffix" required>
              <n-select
                id="suffix"
                v-model:value="formData.basic.suffix"
                :options="suffixOptions"
                :disabled="!formData.basic.category || suffixOptions.length === 0 "
                placeholder="请选择尾缀"
                style="width: 200px"
                filterable
                clearable
              />
            </n-form-item>
          </n-form>
        </div>

        <div v-show="currentStep === 2">
          <n-form label-placement="left" label-width="140px">
            <n-grid :cols="24" :x-gap="24">
              <!-- 适用OE区域 -->
              <n-gi :span="24">
                <n-divider title-placement="left" class="full-width-divider">
                  <span style="font-weight: bold; font-size: 14px">适用OE</span>
                  <n-button
                    size="small"
                    type="primary"
                    class="add-brand-btn"
                    @click="showOESelector = true"
                  >
                    <template #icon>
                      <n-icon><AddCircleOutline /></n-icon>
                    </template>
                    添加OE
                  </n-button></n-divider
                >
              </n-gi>
              <n-gi :span="24">
                <div v-if="selectedMainNum && selectedMainNum.length > 0" class="mt-4">
                  <h3>已选产品主号：</h3>
                  <n-card v-for="mainNum in selectedMainNum" size="small" class="mb-2">
                    <n-grid :cols="24" :x-gap="12">
                      <n-gi :span="20">
                        <strong>{{ mainNum.Number }}</strong>
                      </n-gi>
                      <!-- 删除按钮放在最右边 -->
                      <n-gi :span="4" class="flex justify-end items-center">
                        <n-button
                          type="error"
                          size="small"
                          ghost
                          @click="removeMainNumAndAssociatedVehicles(mainNum.Number)"
                        >
                          删除
                        </n-button>
                      </n-gi>
                    </n-grid>
                  </n-card>
                </div>
                <div v-else class="mt-4 text-muted">
                  <p>暂无已选OE</p>
                </div>
              </n-gi>

              <!-- 参考品牌区域 -->
              <n-gi :span="24">
                <n-divider title-placement="left" class="full-width-divider">
                  <span style="font-weight: bold; font-size: 14px">参考品牌</span>
                  <n-button
                    size="small"
                    type="primary"
                    class="add-brand-btn"
                    @click="addCompetitorInfo"
                  >
                    <template #icon>
                      <n-icon><AddCircleOutline /></n-icon>
                    </template>
                    添加竞品品牌
                  </n-button></n-divider
                >
              </n-gi>

              <!-- 参考品牌列表容器 -->
              <n-gi :span="24">
                <div class="brand-list-container">
                  <div
                    v-for="(brand, index) in formData.reference.competitors"
                    :key="index"
                    :ref="
                      (el) => {
                        if (index === formData.reference.competitors.length - 1) lastBrandRef = el
                      }
                    "
                    class="brand-item"
                  >
                    <n-grid :cols="24" :x-gap="12">
                      <n-gi :span="10">
                        <n-form-item label="品牌名称" required>
                          <n-select
                            v-model:value="brand.CompetitorBrand"
                            :options="article_brand_type.map((c) => ({ label: c, value: c }))"
                          />
                        </n-form-item>
                      </n-gi>
                      <n-gi :span="10">
                        <n-form-item label="参考号" required>
                          <n-input v-model:value="brand.Cem" />
                        </n-form-item>
                      </n-gi>
                      <n-gi :span="4">
                        <n-button circle type="error" secondary @click="removeBrand(index)">
                          <template #icon>
                            <n-icon><Close /></n-icon>
                          </template>
                        </n-button>
                      </n-gi>
                    </n-grid>
                  </div>
                </div>
              </n-gi>
              <!-- 适用车型区域 -->
              <n-gi :span="24">
                <n-divider title-placement="left" class="full-width-divider">
                  <span style="font-weight: bold; font-size: 14px">适用车型</span>
                  <n-button
                    size="small"
                    type="primary"
                    class="add-brand-btn"
                    @click="showVehicleSelector = true"
                  >
                    <template #icon>
                      <n-icon><AddCircleOutline /></n-icon>
                    </template>
                    添加车型
                  </n-button></n-divider
                >
              </n-gi>
              <n-gi :span="24">
                <div v-if="selectedVehicles && selectedVehicles.length > 0" class="mt-4">
                  <h3>已选车型列表：</h3>
                  <n-card
                    v-for="car in selectedVehicles"
                    :key="car.reach_car_id"
                    :title="car.reach_car_id"
                    size="small"
                    class="mb-2"
                  >
                    <div class="">
                      <n-grid :cols="24" :x-gap="12">
                        <n-gi :span="4"> <strong>品牌：</strong> {{ car.brand }} </n-gi>
                        <n-gi :span="4"> <strong>车系：</strong> {{ car.series }} </n-gi>
                        <n-gi :span="4"> <strong>车型：</strong> {{ car.model }} </n-gi>
                        <n-gi :span="4"> <strong>年款：</strong> {{ car.model_year }} </n-gi>
                        <n-gi :span="4"> <strong>排量：</strong> {{ car.displacement }} </n-gi>
                        <!-- 删除按钮放在最右边 -->
                        <n-gi :span="4" class="flex justify-end items-center">
                          <n-button
                            type="error"
                            size="small"
                            ghost
                            @click="removeVehicles(car.reach_car_id)"
                          >
                            删除
                          </n-button>
                        </n-gi>
                      </n-grid>
                    </div>
                  </n-card>
                </div>
                <div v-else class="mt-4 text-muted">
                  <p>暂无已选车型</p>
                </div>
              </n-gi>
            </n-grid>
          </n-form>
        </div>

        <div v-show="currentStep === 3">
          <n-form label-placement="top">
            <div v-for="(item, index) in logisticsList" :key="index" class="supplier-item">
              <n-space vertical>
                <div class="mb-4 flex items-center justify-between">
                  <n-text strong>供应商 {{ index + 1 }}</n-text>
                  <n-button
                    v-if="index > 0"
                    circle
                    size="small"
                    type="error"
                    @click="removeSupplier(index)"
                  >
                    <template #icon>
                      <n-icon><Close /></n-icon>
                    </template>
                  </n-button>
                </div>

                <n-grid :cols="12" :x-gap="18" :y-gap="12">
                  <n-gi :span="4">
                    <n-form-item label="供应商名称">
                      <n-select
                        v-model:value="item.supplier"
                        :options="supplierOptions"
                        placeholder="请选择供应商"
                        @update:value="handleSupplierChange(item, $event)"
                        style="width: 240px"
                      />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="供应商代码">
                      <n-input
                        :value="item.code"
                        readonly
                        placeholder="自动填充"
                        style="width: 240px; background-color: #f5f5f5"
                      />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="含税价（元）" required>
                      <n-input-number v-model:value="item.price" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="内盒长（mm）" required>
                      <n-input-number v-model:value="item.innerLength" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="内盒宽（mm）" required>
                      <n-input-number v-model:value="item.innerWidth" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="内盒高（mm）" required>
                      <n-input-number v-model:value="item.innerHeight" :min="0" />
                    </n-form-item>
                  </n-gi>

                  <n-gi :span="3">
                    <n-form-item label="单品净重（kg）" required>
                      <n-input-number v-model:value="item.netWeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="单品毛重（kg）" required>
                      <n-input-number v-model:value="item.grossWeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="MPQ" required>
                      <n-input-number v-model:value="item.mpq" :min="1" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="MOQ" required>
                      <n-input-number v-model:value="item.moq" :min="1" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="外箱长（mm）" required>
                      <n-input-number v-model:value="item.boxLength" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="外箱宽（mm）" required>
                      <n-input-number v-model:value="item.boxWidth" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="外箱高（mm）" required>
                      <n-input-number v-model:value="item.boxHeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="整箱毛重（kg）" required>
                      <n-input-number v-model:value="item.boxGrossWeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                </n-grid>
              </n-space>
            </div>

            <div class="mt-4 text-center">
              <n-button type="primary" @click="addSupplier">
                <template #icon>
                  <n-icon><Add /></n-icon>
                </template>
                添加供应商
              </n-button>
            </div>
          </n-form>
        </div>

        <div v-show="currentStep === 4">
          <n-form label-placement="top">
            <n-grid :cols="3" :x-gap="24" :y-gap="16">
              <n-gi v-for="(field, index) in productParamConfig" :key="index">
                <n-form-item :label="field.label">
                  <n-input
                    v-model:value="productParams[field.key]"
                    :placeholder="`请输入${field.label}`"
                  />
                </n-form-item>
              </n-gi>
            </n-grid>
          </n-form>
        </div>

        <div v-show="currentStep === 5">
          <n-grid :cols="3" :x-gap="24" :y-gap="16">
            <n-gi>
              <strong>点击按钮上传附件文件：</strong>
              <n-upload :custom-request="customUpload" list-type="image">
                <n-button>选择文件上传</n-button>
              </n-upload>
            </n-gi>
          </n-grid>
        </div>

        <div v-if="currentStep === 6">
          <n-grid :cols="3" :x-gap="24" :y-gap="16">
            <n-gi>
              <n-form-item label="可供状态">
                <n-radio-group v-model:value="formData.availableInfo.available">
                  <n-space>
                    <n-radio value="available">可供</n-radio>
                    <n-radio value="unavailable">不可供</n-radio>
                  </n-space>
                </n-radio-group>
              </n-form-item>
            </n-gi>

            <n-gi :span="2">
              <n-form-item
                :label="
                  formData.availableInfo.available === 'available' ? '可供备注' : '不可供备注'
                "
              >
                <n-input
                  v-model:value="formData.availableInfo.remark"
                  type="textarea"
                  :placeholder="
                    formData.availableInfo.available === 'available'
                      ? '请输入产品可供的相关说明'
                      : '请输入产品不可供的原因'
                  "
                  :autosize="{ minRows: 2, maxRows: 5 }"
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </div>

        <!-- 操作按钮 -->
        <template #footer>
          <div class="flex items-center justify-between">
            <div>
              <n-button v-if="currentStep === 1" type="primary" @click="openImportModal">
                批量上传
              </n-button>
            </div>
            <div class="flex gap-4">
              <n-button @click="currentStep > 1 ? handlePrev() : (showCreateModal = false)">
                {{ currentStep > 1 ? '上一步' : '取消' }}
              </n-button>
              <n-button
                type="primary"
                :disabled="currentStep === 1 && !formRef?.validate"
                @click="currentStep === 6 ? submitProduct() : handleNext()"
              >
                {{ currentStep === 6 ? '完成' : '下一步' }}
              </n-button>
            </div>
          </div>
        </template>
      </n-card>
    </n-modal>

    <NModal
      v-model:show="showModal"
      preset="card"
      :style="{ width: '1200px', maxHeight: '85vh' }"
      :closable="true"
      :mask-closable="false"
    >
      <template #header>
        <div class="modal-header">
          <div class="product-title">
            <n-icon size="20" color="#21438C" style="margin-right: 8px">
              <svg viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"
                />
              </svg>
            </n-icon>
            <span class="title-text">产品详情信息</span>
            <n-tag
              v-if="currentRowData && currentRowData.MaterialCode"
              type="info"
              size="medium"
              style="margin-left: 12px; font-size: 14px; padding: 4px 12px"
            >
              {{ currentRowData.MaterialCode }}
            </n-tag>
          </div>
        </div>
      </template>

      <div class="product-detail-container">
        <!-- 导航选项卡 -->
        <n-tabs
          v-model:value="currentTabValue"
          type="card"
          default-value="basic"
          size="medium"
          class="product-detail-tabs"
        >
          <!-- 基础信息 -->
          <n-tab-pane name="basic" tab="基础信息">
            <div class="tab-content-wrapper">
              <div class="product-detail-flex">
                <!-- 左侧属性表格 -->
                <div class="product-detail-table-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品基础信息</span>
                  </div>
                  <div class="product-detail-table">
                    <table class="detail-table">
                      <tbody>
                        <tr v-for="(row, idx) in productAttributeRows" :key="idx">
                          <td class="detail-label">{{ row.label }}</td>
                          <td class="detail-value">{{ row.value }}</td>
                          <td v-if="row.label2" class="detail-label">{{ row.label2 }}</td>
                          <td v-if="row.label2" class="detail-value">{{ row.value2 }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- 右侧图片区 -->
                <div class="product-image-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品图片</span>
                    <!-- 调试信息 -->
                    <small style="margin-left: 10px; color: #999">
                      (简图: {{ productDiagram ? '有' : '无' }}, 实物图:
                      {{ productPhotoList.length }}张)
                    </small>
                  </div>

                  <!-- 产品简图 -->
                  <div v-if="productDiagram" class="product-diagram-section">
                    <div class="image-section-title">产品简图</div>
                    <div class="product-detail-image">
                      <img
                        :src="productDiagram"
                        alt="产品简图"
                        class="product-image clickable-image"
                        @click="handleImageClick(productDiagram)"
                      />
                    </div>
                  </div>

                  <!-- 产品实物图轮播 -->
                  <div v-if="productPhotoList.length > 0" class="product-photos-section">
                    <div class="image-section-title">产品实物图</div>
                    <div class="product-carousel-wrapper">
                      <n-carousel
                        v-model:current-index="currentCarouselIndex"
                        :show-dots="productPhotoList.length > 1"
                        :show-arrow="productPhotoList.length > 1"
                        :autoplay="false"
                        class="product-carousel"
                      >
                        <div
                          v-for="(photo, index) in productPhotoList"
                          :key="index"
                          class="carousel-item"
                        >
                          <img
                            :src="photo"
                            :alt="`产品实物图 ${index + 1}`"
                            class="product-image clickable-image"
                            @click="handleCarouselImageClick(photo, index)"
                          />
                        </div>
                      </n-carousel>
                      <div v-if="productPhotoList.length > 1" class="carousel-counter">
                        {{ currentCarouselIndex + 1 }} / {{ productPhotoList.length }}
                      </div>
                    </div>
                  </div>

                  <!-- 无图片占位符 -->
                  <div
                    v-if="!productDiagram && productPhotoList.length === 0"
                    class="no-image-placeholder"
                  >
                    <n-icon size="48" color="#ccc">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 参考信息 -->
          <n-tab-pane name="reference" tab="参考信息">
            <div class="tab-content-wrapper">
              <div class="product-detail-flex">
                <!-- 左侧内容区 -->
                <div class="product-detail-table-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>参考信息</span>
                  </div>
                  <n-spin :show="referenceInfoLoading">
                    <div v-if="referenceInfoData.length > 0" class="data-table-wrapper">
                      <n-data-table
                        :columns="getReferenceInfoColumns()"
                        :data="referenceInfoData"
                        :max-height="400"
                        size="small"
                        striped
                        :bordered="true"
                      />
                    </div>
                    <n-empty v-else description="未查询到参考信息数据" />
                  </n-spin>
                </div>

                <!-- 右侧图片区 -->
                <div class="product-image-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品图片</span>
                  </div>

                  <!-- 产品简图 -->
                  <div v-if="productDiagram" class="product-diagram-section">
                    <div class="image-section-title">产品简图</div>
                    <div class="product-detail-image">
                      <img
                        :src="productDiagram"
                        alt="产品简图"
                        class="product-image clickable-image"
                        @click="handleImageClick(productDiagram)"
                      />
                    </div>
                  </div>

                  <!-- 产品实物图轮播 -->
                  <div v-if="productPhotoList.length > 0" class="product-photos-section">
                    <div class="image-section-title">产品实物图</div>
                    <div class="product-carousel-wrapper">
                      <n-carousel
                        v-model:current-index="currentCarouselIndex"
                        :show-dots="productPhotoList.length > 1"
                        :show-arrow="productPhotoList.length > 1"
                        :autoplay="false"
                        class="product-carousel"
                      >
                        <div
                          v-for="(photo, index) in productPhotoList"
                          :key="index"
                          class="carousel-item"
                        >
                          <img
                            :src="photo"
                            :alt="`产品实物图 ${index + 1}`"
                            class="product-image clickable-image"
                            @click="handleCarouselImageClick(photo, index)"
                          />
                        </div>
                      </n-carousel>
                      <div v-if="productPhotoList.length > 1" class="carousel-counter">
                        {{ currentCarouselIndex + 1 }} / {{ productPhotoList.length }}
                      </div>
                    </div>
                  </div>

                  <!-- 无图片占位符 -->
                  <div
                    v-if="!productDiagram && productPhotoList.length === 0"
                    class="no-image-placeholder"
                  >
                    <n-icon size="48" color="#ccc">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 产品属性详情 -->
          <n-tab-pane name="attributes" tab="产品属性详情">
            <div class="tab-content-wrapper">
              <div class="product-detail-flex">
                <!-- 左侧内容区 -->
                <div class="product-detail-table-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M12,2A2,2 0 0,1 14,4V6H20A2,2 0 0,1 22,8V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V8A2,2 0 0,1 4,6H10V4A2,2 0 0,1 12,2M14,8V19H20V8H14M4,8V19H12V8H4Z"
                        />
                      </svg>
                    </n-icon>
                    <span>详细属性信息</span>
                  </div>
                  <div v-if="detailRows.length > 0" class="product-detail-table">
                    <table class="detail-table">
                      <tbody>
                        <tr v-for="(row, idx) in detailRows" :key="idx">
                          <td class="detail-label" :class="row.labelClass">{{ row.label }}</td>
                          <td class="detail-value" :class="row.valueClass">{{ row.value }}</td>
                          <td v-if="row.label2" class="detail-label" :class="row.label2Class">
                            {{ row.label2 }}
                          </td>
                          <td v-if="row.label2" class="detail-value" :class="row.value2Class">
                            {{ row.value2 }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <n-empty v-else description="未查询到产品属性详情数据" />
                </div>

                <!-- 右侧图片区 -->
                <div class="product-image-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品图片</span>
                  </div>

                  <!-- 产品简图 -->
                  <div v-if="productDiagram" class="product-diagram-section">
                    <div class="image-section-title">产品简图</div>
                    <div class="product-detail-image">
                      <img
                        :src="productDiagram"
                        alt="产品简图"
                        class="product-image clickable-image"
                        @click="handleImageClick(productDiagram)"
                      />
                    </div>
                  </div>

                  <!-- 产品实物图轮播 -->
                  <div v-if="productPhotoList.length > 0" class="product-photos-section">
                    <div class="image-section-title">产品实物图</div>
                    <div class="product-carousel-wrapper">
                      <n-carousel
                        v-model:current-index="currentCarouselIndex"
                        :show-dots="productPhotoList.length > 1"
                        :show-arrow="productPhotoList.length > 1"
                        :autoplay="false"
                        class="product-carousel"
                      >
                        <div
                          v-for="(photo, index) in productPhotoList"
                          :key="index"
                          class="carousel-item"
                        >
                          <img
                            :src="photo"
                            :alt="`产品实物图 ${index + 1}`"
                            class="product-image clickable-image"
                            @click="handleCarouselImageClick(photo, index)"
                          />
                        </div>
                      </n-carousel>
                      <div v-if="productPhotoList.length > 1" class="carousel-counter">
                        {{ currentCarouselIndex + 1 }} / {{ productPhotoList.length }}
                      </div>
                    </div>
                  </div>

                  <!-- 无图片占位符 -->
                  <div
                    v-if="!productDiagram && productPhotoList.length === 0"
                    class="no-image-placeholder"
                  >
                    <n-icon size="48" color="#ccc">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 替代说明 -->
          <n-tab-pane name="replacement" tab="替代说明">
            <div class="tab-content-wrapper">
              <div class="product-detail-flex">
                <!-- 左侧内容区 -->
                <div class="product-detail-table-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M21,11C21,16.55 17.16,21.74 12,23C6.84,21.74 3,16.55 3,11V5L12,1L21,5V11M12,21C15.75,20 19,15.54 19,11.22V6.3L12,3.18L5,6.3V11.22C5,15.54 8.25,20 12,21Z"
                        />
                      </svg>
                    </n-icon>
                    <span>替代说明</span>
                  </div>
                  <n-spin :show="replacementInfoLoading">
                    <div v-if="replacementInfoData.length > 0" class="data-table-wrapper">
                      <n-data-table
                        :columns="getReplacementInfoColumns()"
                        :data="replacementInfoData"
                        :max-height="400"
                        size="small"
                        striped
                        :bordered="true"
                      />
                    </div>
                    <n-empty v-else description="未查询到替代说明数据" />
                  </n-spin>
                </div>

                <!-- 右侧图片区 -->
                <div class="product-image-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品图片</span>
                  </div>

                  <!-- 产品简图 -->
                  <div v-if="productDiagram" class="product-diagram-section">
                    <div class="image-section-title">产品简图</div>
                    <div class="product-detail-image">
                      <img
                        :src="productDiagram"
                        alt="产品简图"
                        class="product-image clickable-image"
                        @click="handleImageClick(productDiagram)"
                      />
                    </div>
                  </div>

                  <!-- 产品实物图轮播 -->
                  <div v-if="productPhotoList.length > 0" class="product-photos-section">
                    <div class="image-section-title">产品实物图</div>
                    <div class="product-carousel-wrapper">
                      <n-carousel
                        v-model:current-index="currentCarouselIndex"
                        :show-dots="productPhotoList.length > 1"
                        :show-arrow="productPhotoList.length > 1"
                        :autoplay="false"
                        class="product-carousel"
                      >
                        <div
                          v-for="(photo, index) in productPhotoList"
                          :key="index"
                          class="carousel-item"
                        >
                          <img
                            :src="photo"
                            :alt="`产品实物图 ${index + 1}`"
                            class="product-image clickable-image"
                            @click="handleCarouselImageClick(photo, index)"
                          />
                        </div>
                      </n-carousel>
                      <div v-if="productPhotoList.length > 1" class="carousel-counter">
                        {{ currentCarouselIndex + 1 }} / {{ productPhotoList.length }}
                      </div>
                    </div>
                  </div>

                  <!-- 无图片占位符 -->
                  <div
                    v-if="!productDiagram && productPhotoList.length === 0"
                    class="no-image-placeholder"
                  >
                    <n-icon size="48" color="#ccc">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- Reach-供应商对照 -->
          <n-tab-pane name="reach-supplier" tab="Reach-供应商对照">
            <div class="tab-content-wrapper">
              <div class="product-detail-flex">
                <!-- 左侧内容区 -->
                <div class="product-detail-table-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M12,5.5A3.5,3.5 0 0,1 15.5,9A3.5,3.5 0 0,1 12,12.5A3.5,3.5 0 0,1 8.5,9A3.5,3.5 0 0,1 12,5.5M5,8C5.56,8 6.08,8.15 6.53,8.42C6.38,9.85 6.8,11.27 7.66,12.38C7.16,13.34 6.16,14 5,14A3,3 0 0,1 2,11A3,3 0 0,1 5,8M19,8A3,3 0 0,1 22,11A3,3 0 0,1 19,14C17.84,14 16.84,13.34 16.34,12.38C17.2,11.27 17.62,9.85 17.47,8.42C17.92,8.15 18.44,8 19,8M5.5,18.25C5.5,16.18 8.41,14.5 12,14.5C15.59,14.5 18.5,16.18 18.5,18.25V20H5.5V18.25M0,20V18.5C0,17.11 1.89,15.94 4.45,15.6C3.86,16.28 3.5,17.22 3.5,18.25V20H0M24,20H20.5V18.25C20.5,17.22 20.14,16.28 19.55,15.6C22.11,15.94 24,17.11 24,18.5V20Z"
                        />
                      </svg>
                    </n-icon>
                    <span>Reach-供应商对照</span>
                  </div>
                  <n-spin :show="priceCategoryInfoLoading">
                    <div v-if="priceCategoryInfoData.length > 0" class="data-table-wrapper">
                      <n-data-table
                        :columns="getPriceCategoryInfoColumns()"
                        :data="priceCategoryInfoData"
                        :max-height="400"
                        size="small"
                        striped
                        :bordered="true"
                      />
                    </div>
                    <n-empty v-else description="未查询到Reach-供应商对照数据" />
                  </n-spin>
                </div>

                <!-- 右侧图片区 -->
                <div class="product-image-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品图片</span>
                  </div>

                  <!-- 产品简图 -->
                  <div v-if="productDiagram" class="product-diagram-section">
                    <div class="image-section-title">产品简图</div>
                    <div class="product-detail-image">
                      <img
                        :src="productDiagram"
                        alt="产品简图"
                        class="product-image clickable-image"
                        @click="handleImageClick(productDiagram)"
                      />
                    </div>
                  </div>

                  <!-- 产品实物图轮播 -->
                  <div v-if="productPhotoList.length > 0" class="product-photos-section">
                    <div class="image-section-title">产品实物图</div>
                    <div class="product-carousel-wrapper">
                      <n-carousel
                        v-model:current-index="currentCarouselIndex"
                        :show-dots="productPhotoList.length > 1"
                        :show-arrow="productPhotoList.length > 1"
                        :autoplay="false"
                        class="product-carousel"
                      >
                        <div
                          v-for="(photo, index) in productPhotoList"
                          :key="index"
                          class="carousel-item"
                        >
                          <img
                            :src="photo"
                            :alt="`产品实物图 ${index + 1}`"
                            class="product-image clickable-image"
                            @click="handleCarouselImageClick(photo, index)"
                          />
                        </div>
                      </n-carousel>
                      <div v-if="productPhotoList.length > 1" class="carousel-counter">
                        {{ currentCarouselIndex + 1 }} / {{ productPhotoList.length }}
                      </div>
                    </div>
                  </div>

                  <!-- 无图片占位符 -->
                  <div
                    v-if="!productDiagram && productPhotoList.length === 0"
                    class="no-image-placeholder"
                  >
                    <n-icon size="48" color="#ccc">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 供应商物流信息 -->
          <n-tab-pane name="logistics" tab="供应商物流信息">
            <div class="tab-content-wrapper">
              <div class="product-detail-flex">
                <!-- 左侧内容区 -->
                <div class="product-detail-table-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M3,4A2,2 0 0,0 1,6V17H3A3,3 0 0,0 6,20A3,3 0 0,0 9,17H15A3,3 0 0,0 18,20A3,3 0 0,0 21,17H23V12L20,8H17V4M10,6L14,10L10,14V11H5V9H10M17,9.5H19.5L21.47,12H17M6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5M18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5Z"
                        />
                      </svg>
                    </n-icon>
                    <span>供应商物流信息</span>
                  </div>
                  <n-spin :show="supplyListLoading">
                    <div v-if="supplyListData.length > 0" class="data-table-wrapper">
                      <n-data-table
                        :columns="getSupplyListColumns()"
                        :data="supplyListData"
                        :max-height="400"
                        size="small"
                        striped
                        :bordered="true"
                      />
                    </div>
                    <n-empty v-else description="未查询到供应商物流信息数据" />
                  </n-spin>
                </div>

                <!-- 右侧图片区 -->
                <div class="product-image-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品图片</span>
                  </div>

                  <!-- 产品简图 -->
                  <div v-if="productDiagram" class="product-diagram-section">
                    <div class="image-section-title">产品简图</div>
                    <div class="product-detail-image">
                      <img
                        :src="productDiagram"
                        alt="产品简图"
                        class="product-image clickable-image"
                        @click="handleImageClick(productDiagram)"
                      />
                    </div>
                  </div>

                  <!-- 产品实物图轮播 -->
                  <div v-if="productPhotoList.length > 0" class="product-photos-section">
                    <div class="image-section-title">产品实物图</div>
                    <div class="product-carousel-wrapper">
                      <n-carousel
                        v-model:current-index="currentCarouselIndex"
                        :show-dots="productPhotoList.length > 1"
                        :show-arrow="productPhotoList.length > 1"
                        :autoplay="false"
                        class="product-carousel"
                      >
                        <div
                          v-for="(photo, index) in productPhotoList"
                          :key="index"
                          class="carousel-item"
                        >
                          <img
                            :src="photo"
                            :alt="`产品实物图 ${index + 1}`"
                            class="product-image clickable-image"
                            @click="handleImageClick(photo)"
                          />
                        </div>
                      </n-carousel>
                      <div v-if="productPhotoList.length > 1" class="carousel-counter">
                        {{ currentCarouselIndex + 1 }} / {{ productPhotoList.length }}
                      </div>
                    </div>
                  </div>

                  <!-- 无图片占位符 -->
                  <div
                    v-if="!productDiagram && productPhotoList.length === 0"
                    class="no-image-placeholder"
                  >
                    <n-icon size="48" color="#ccc">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 车型详情 -->
          <n-tab-pane name="vehicle" tab="车型详情">
            <div class="tab-content-wrapper">
              <div class="product-detail-flex">
                <!-- 左侧内容区 -->
                <div class="product-detail-table-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M18.92,6.01C18.72,5.42 18.16,5 17.5,5H15V4A2,2 0 0,0 13,2H11A2,2 0 0,0 9,4V5H6.5C5.84,5 5.28,5.42 5.08,6.01L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6.01M6.5,7H17.5L19,11H5L6.5,7M11,4H13V5H11V4M7.5,16A1.5,1.5 0 0,1 6,14.5A1.5,1.5 0 0,1 7.5,13A1.5,1.5 0 0,1 9,14.5A1.5,1.5 0 0,1 7.5,16M16.5,16A1.5,1.5 0 0,1 15,14.5A1.5,1.5 0 0,1 16.5,13A1.5,1.5 0 0,1 18,14.5A1.5,1.5 0 0,1 16.5,16Z"
                        />
                      </svg>
                    </n-icon>
                    <span>适用车型信息</span>
                  </div>
                  <div class="vehicle-info-summary">
                    <n-statistic label="适用车型总数" :value="carsdata.length" />
                    <n-statistic label="VIO总量" :value="allviocount" />
                  </div>
                  <div class="vehicle-table-wrapper">
                    <n-data-table
                      :columns="carscolumns"
                      :data="carsdata"
                      :max-height="350"
                      virtual-scroll
                      :row-props="rowProps"
                      size="small"
                      striped
                    />
                  </div>
                </div>

                <!-- 右侧图片区 -->
                <div class="product-image-wrapper">
                  <div class="section-title">
                    <n-icon size="16" color="#21438C">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <span>产品图片</span>
                  </div>

                  <!-- 产品简图 -->
                  <div v-if="productDiagram" class="product-diagram-section">
                    <div class="image-section-title">产品简图</div>
                    <div class="product-detail-image">
                      <img
                        :src="productDiagram"
                        alt="产品简图"
                        class="product-image clickable-image"
                        @click="handleImageClick(productDiagram)"
                      />
                    </div>
                  </div>

                  <!-- 产品实物图轮播 -->
                  <div v-if="productPhotoList.length > 0" class="product-photos-section">
                    <div class="image-section-title">产品实物图</div>
                    <div class="product-carousel-wrapper">
                      <n-carousel
                        v-model:current-index="currentCarouselIndex"
                        :show-dots="productPhotoList.length > 1"
                        :show-arrow="productPhotoList.length > 1"
                        :autoplay="false"
                        class="product-carousel"
                      >
                        <div
                          v-for="(photo, index) in productPhotoList"
                          :key="index"
                          class="carousel-item"
                        >
                          <img
                            :src="photo"
                            :alt="`产品实物图 ${index + 1}`"
                            class="product-image clickable-image"
                            @click="handleImageClick(photo)"
                          />
                        </div>
                      </n-carousel>
                      <div v-if="productPhotoList.length > 1" class="carousel-counter">
                        {{ currentCarouselIndex + 1 }} / {{ productPhotoList.length }}
                      </div>
                    </div>
                  </div>

                  <!-- 无图片占位符 -->
                  <div
                    v-if="!productDiagram && productPhotoList.length === 0"
                    class="no-image-placeholder"
                  >
                    <n-icon size="48" color="#ccc">
                      <svg viewBox="0 0 24 24">
                        <path
                          fill="currentColor"
                          d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                        />
                      </svg>
                    </n-icon>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 备注 -->
          <n-tab-pane name="remarks" tab="备注">
            <n-empty description="备注功能开发中，等待后端接口适配" />
          </n-tab-pane>
        </n-tabs>
      </div>
    </NModal>
    <n-modal
      v-model:show="showBatchImportModal"
      preset="card"
      :style="{ width: '900px' }"
      :title="modalTitle"
      :mask-closable="false"
      :closable="true"
      @after-leave="resetAllState"
    >
      <n-spin :show="BatchImportLoading">
        <n-steps
          :current="BatchImportCurrentStep"
          :status="BatchImportStepStatus"
          style="margin-bottom: 20px"
        >
          <n-step title="上传文件" description="上传数据文件并填写备注" />
          <n-step title="预览确认" description="预览处理结果并确认生成" />
          <n-step title="导入结果" description="查看最终导入结果" />
        </n-steps>

        <div v-if="BatchImportCurrentStep === 1">
          <n-form-item label="备注">
            <n-input
              v-model:value="remarks"
              type="textarea"
              placeholder="请输入备注信息 (可选)"
              :autosize="{ minRows: 3 }"
            />
          </n-form-item>
          <n-form-item label="模板下载">
            <n-button @click="downloadTemplate">下载模板</n-button>
          </n-form-item>
          <n-form-item label="选择文件">
            <n-upload
              v-model:file-list="fileList"
              action="#"
              :default-upload="false"
              :max="1"
              accept=".csv,.xlsx,.xls"
              @change="handleFileChange"
            >
              <n-button>选择文件</n-button>
            </n-upload>
          </n-form-item>
          <n-space justify="end" style="margin-top: 20px">
            <n-button type="primary" :disabled="!selectedFile" @click="uploadAndPreview"
              >上传并预览</n-button
            >
            <n-button @click="closeModal">取消</n-button>
          </n-space>
        </div>

        <div v-if="BatchImportCurrentStep === 2">
          <n-h4>数据预览与处理结果</n-h4>
          <n-data-table
            :columns="previewTable.columns"
            :data="previewTable.data"
            :pagination="false"
            :max-height="300"
            :bordered="true"
            :scroll-x="1200"
            :single-line="false"
            style="width: 100%"
          />
          <n-space justify="end" style="margin-top: 20px">
            <n-button @click="exportResults(previewTable.data, 'preview_data')"
              >导出预览结果</n-button
            >
            <n-button type="primary" @click="confirmGeneration">确定生成</n-button>
            <n-button @click="BatchImportCurrentStep = 1">上一步</n-button>
            <n-button @click="closeModal">取消</n-button>
          </n-space>
        </div>

        <div v-if="BatchImportCurrentStep === 3">
          <n-h4>最终生成结果</n-h4>
          <n-data-table
            :columns="finalTable.columns"
            :data="finalTable.data"
            :pagination="false"
            :max-height="300"
            :bordered="true"
          />
          <n-space justify="end" style="margin-top: 20px">
            <n-button @click="exportResults(finalTable.data, 'final_generated_results')"
              >导出生成结果</n-button
            >
            <n-button type="primary" :loading="importToDbLoading" @click="completeImportToDatabase"
              >完成入库</n-button
            >
            <n-button @click="BatchImportCurrentStep = 2">上一步</n-button>
            <n-button @click="closeModal">取消</n-button>
          </n-space>
        </div>
      </n-spin>
    </n-modal>

    <!-- 图片预览模态框 -->
    <n-modal
      v-model:show="showImagePreview"
      :mask-closable="false"
      :closable="false"
      :style="{ width: '95vw', height: '95vh' }"
      class="image-preview-modal"
    >
      <div class="image-preview-wrapper">
        <!-- 头部工具栏 -->
        <div class="preview-header">
          <div class="preview-title">
            <span>图片预览</span>
            <span v-if="previewImageList.length > 1" class="preview-counter">
              {{ currentPreviewIndex + 1 }} / {{ previewImageList.length }}
            </span>
          </div>
          <div class="preview-controls">
            <n-button
              v-if="previewImageList.length > 1"
              @click="switchPreviewImage('prev')"
              size="small"
              type="primary"
              ghost
            >
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"
                  />
                </svg>
              </n-icon>
              上一张
            </n-button>
            <n-button
              v-if="previewImageList.length > 1"
              @click="switchPreviewImage('next')"
              size="small"
              type="primary"
              ghost
            >
              下一张
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
                  />
                </svg>
              </n-icon>
            </n-button>
            <n-button @click="resetImageState" size="small" ghost>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M18,11H13L14.5,9.5L13.08,8.08L9.16,12L13.08,15.92L14.5,14.5L13,13H18V11Z"
                  />
                </svg>
              </n-icon>
              重置
            </n-button>
            <n-button @click="closeImagePreview" size="small" type="error" ghost>
              <n-icon><Close /></n-icon>
              关闭
            </n-button>
          </div>
        </div>

        <!-- 图片容器 -->
        <div
          class="image-preview-container"
          @wheel="handleImageWheel"
          @mousemove="handleImageMouseMove"
          @mouseup="handleImageMouseUp"
          @mouseleave="handleImageMouseUp"
        >
          <img
            :src="previewImageUrl"
            alt="图片预览"
            class="preview-image"
            :style="{
              transform: `scale(${imageScale}) translate(${imagePosition.x / imageScale}px, ${
                imagePosition.y / imageScale
              }px)`,
              cursor: imageScale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default',
            }"
            @mousedown="handleImageMouseDown"
            @dragstart.prevent
          />
        </div>

        <!-- 底部提示 -->
        <div class="preview-footer">
          <div class="preview-tips">
            <span>💡 快捷键：</span>
            <span>滚轮/±键缩放 ({{ Math.round(imageScale * 100) }}%)</span>
            <span v-if="imageScale > 1">• 拖拽移动</span>
            <span v-if="previewImageList.length > 1">• ←→切换图片</span>
            <span>• 0键重置 • ESC关闭</span>
          </div>
        </div>
      </div>
    </n-modal>
  </CommonPage>
</template>

<style>
.n-form-item-label {
  white-space: nowrap;
  font-size: 13px;
}

.n-input .n-input-number {
  width: 100% !important;
}

/* 表格悬停效果 */
.n-data-table-tr--hover {
  background-color: #f8f8f8;
  transition: background-color 0.2s;
}

/* 弹窗滚动条样式 */
.n-card__content::-webkit-scrollbar {
  width: 6px;
}
.n-card__content::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 3px;
}
.steps-nav {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 16px;
}
.font-bold {
  font-weight: 600;
  color: #333;
}
.brand-item {
  padding: 12px 8px;
  margin-bottom: 16px;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s;
  margin: 0 8px 12px 0;
  box-sizing: border-box;
}

.vehicle-item {
  padding: 12px 8px;
  margin-bottom: 16px;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s;
  margin: 0 8px 12px 0;
  box-sizing: border-box;
}

.n-grid {
  margin: 0 !important; /* 移除栅格系统外边距 */
  padding: 0 6px !important;
}

.brand-item:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}
.brand-list-container {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
  margin: 12px -8px 12px 0;
  padding-right: 8px;
}

/* 调整删除按钮对齐 */
.n-form-item .n-input-group {
  align-items: center;
}

.n-form-item .n-input {
  min-width: auto !important; /* 移除输入框最小宽度限制 */
}

.n-grid--x-gap-12 {
  gap: 0 12px !important; /* 保持垂直间距，调整水平间距 */
  margin: 0 -6px !important;
  width: calc(100% + 12px);
}

.n-form-item .n-form-item-label {
  width: 80px !important;
  justify-content: flex-start !important;
}

.n-modal .n-card {
  max-height: 90vh !important; /* 增加可视区域 */
  display: flex;
  flex-direction: column;
}

/* 品牌项间距优化 */
.brand-item {
  margin-bottom: 12px;
  padding: 12px;
}

/* Dialog容器高度控制 */
.n-card {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.n-card__content {
  flex: 1;
  overflow: auto !important;
  display: flex;
  flex-direction: column;
}

/* 统一布局样式 */
.n-form-item .n-form-item-label {
  width: 80px !important;
  justify-content: flex-start !important;
}

.brand-list-container {
  overflow-x: hidden;
  max-height: 200px;
  overflow-y: auto;
  margin: 12px 0;
  padding-right: 8px;
}

.full-width-divider {
  width: calc(100% + 40px);
  margin-left: -20px;
  padding: 0 20px;
}

.add-brand-btn {
  position: absolute;
  right: 24px;
  transform: translateY(-50%);
}

.brand-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  background: #f8f8f8;
}

/* 固定底部表单位置 */
.sticky-bottom-form {
  position: sticky;
  bottom: 0;
  background: white;
  padding-top: 16px;
  z-index: 1;
}

.text-blue-500 {
  color: #3b82f6;
}
.hover\:cursor-pointer:hover {
  cursor: pointer;
  text-decoration: underline;
}

.supplier-item {
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.n-form-item-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.n-form-item-label__required::before {
  color: #ff4d4f;
  margin-right: 4px;
}

.dynamic-table-container {
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px); /* 示例：使其占据一定高度以测试表格 flex-height */
}

.table-toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.column-selector-panel {
  min-width: 200px;
  max-width: 300px;
}

/* 产品详情弹框样式优化 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.title-text {
  color: #21438c;
  font-weight: 600;
}

.product-detail-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.product-detail-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-detail-tabs .n-tabs-nav {
  flex-shrink: 0;
}

.product-detail-tabs .n-tabs-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
}

.product-detail-tabs .n-tab-pane {
  height: 100%;
  overflow-y: auto;
  padding: 0;
}

.tab-content-wrapper {
  padding: 16px;
  height: auto;
  min-height: 100%;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-title span {
  margin-left: 8px;
}

.product-detail-flex {
  display: flex;
  gap: 24px;
  min-height: 100%;
}

.product-detail-table-wrapper {
  flex: 1;
  min-width: 0;
  overflow: visible;
}

.product-image-wrapper {
  flex: 0 0 320px;
  display: flex;
  flex-direction: column;
}

.product-detail-image {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
}

.product-image {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-image-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  color: #999;
  min-height: 200px;
}

.no-image-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.vehicle-info-summary {
  display: flex;
  gap: 32px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.vehicle-table-wrapper,
.data-table-wrapper {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

/* 确保Naive UI tabs组件的滚动正常工作 */
.product-detail-tabs .n-tabs .n-tabs-content .n-tab-pane {
  overflow-y: auto !important;
  height: 100% !important;
}

.product-detail-tabs .n-tabs .n-tabs-content {
  height: 100% !important;
  overflow: hidden !important;
}

/* 修复可能的滚动问题 */
.product-detail-container .n-card__content {
  height: 100%;
  overflow: hidden;
  padding: 0;
}

.column-selector-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  /* width: 100%; */
}

.column-selector-item .n-checkbox {
  width: 100%; /* 让 label 也可以点击触发 */
}

.column-selector-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-top: 1px solid #eee;
  margin-top: 8px;
}

.product-detail-flex {
  display: flex;
  flex-direction: row;
  gap: 32px;
  align-items: flex-start;
}

.product-detail-table {
  flex: 1;
  min-width: 0;
}

.detail-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
}

.detail-table td {
  border: 1px solid #d9d9d9;
  padding: 6px 12px;
  font-size: 15px;
  min-width: 120px;
  vertical-align: middle;
}

.detail-label {
  font-weight: 600;
  background: #f7f8fa;
  color: #333;
  text-align: right;
  width: 140px;
}

.detail-value {
  color: #333;
  background: #fff;
  text-align: left;
  width: 180px;
}

.product-detail-image {
  min-width: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

/* 新增图片相关样式 */
.image-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 12px;
  padding-left: 4px;
  border-left: 3px solid #21438c;
}

.product-diagram-section {
  margin-bottom: 20px;
}

.product-photos-section {
  margin-bottom: 20px;
}

.product-carousel-wrapper {
  position: relative;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
}

.product-carousel {
  height: 250px;
}

.carousel-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.carousel-counter {
  position: absolute;
  bottom: 8px;
  right: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
}

.clickable-image {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.clickable-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 图片预览样式 */
.image-preview-modal .n-modal {
  padding: 0;
}

.image-preview-wrapper {
  display: flex;
  flex-direction: column;
  height: 95vh;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e8e8e8;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.preview-counter {
  background: #21438c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: normal;
}

.preview-controls {
  display: flex;
  gap: 8px;
}

.image-preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  background: #000;
  user-select: none;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.1s ease-out;
  transform-origin: center center;
}

.preview-footer {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid #e8e8e8;
}

.preview-tips {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.preview-tips span:first-child {
  color: #21438c;
  font-weight: 600;
}

/* 轮播图箭头样式优化 */
.product-carousel .n-carousel__arrow {
  background: rgba(33, 67, 140, 0.8);
  color: white;
  border: none;
}

.product-carousel .n-carousel__arrow:hover {
  background: rgba(33, 67, 140, 1);
}

/* 轮播图指示点样式优化 */
.product-carousel .n-carousel__dots {
  bottom: -30px;
}

.product-carousel .n-carousel__dot {
  background: #d9d9d9;
}

.product-carousel .n-carousel__dot--active {
  background: #21438c;
}

.vehicle-scroll {
  height: 200px;
  overflow-y: auto;
  padding: 16px;
}
</style>

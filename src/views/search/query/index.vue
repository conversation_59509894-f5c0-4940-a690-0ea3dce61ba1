<template>
  <CommonPage>
    <n-spin :show="searchLoading" size="large" description="正在查询，请稍候...">
      <div class="search-container">
        <div class="search-header-bg">
          <div class="search-tabs">
            <div
              v-for="item in searchTabs"
              :key="item.value"
              :class="['search-tab', { active: searchType === item.value }]"
              @click="searchType = item.value"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="search-panel">
            <n-space align="center">
              <!-- VIN -->
              <n-input
                v-if="searchType === 'VIN'"
                v-model:value="vinSearchValue"
                placeholder="请输入VIN码"
                clearable
                style="width: 540px"
              >
                <template #suffix>
                  <n-button type="primary" :loading="searchLoading" @click="handleSearchClick"
                    >搜索</n-button
                  >
                </template>
              </n-input>
              <!-- OE -->
              <n-input
                v-if="searchType === 'OE'"
                v-model:value="oeSearchValue"
                placeholder="请输入OE号"
                clearable
                style="width: 540px"
              >
                <template #suffix>
                  <n-button type="primary" :loading="searchLoading" @click="handleSearchClick"
                    >搜索</n-button
                  >
                </template>
              </n-input>
              <!-- Car -->
              <template v-if="searchType === 'Car'">
                <div class="car-search-form">
                  <!-- 地区选择 -->
                  <div class="car-search-row">
                    <n-select
                      v-model:value="carQueryItems.country"
                      placeholder="地区*"
                      :options="
                        countryOptions.map((item) => ({
                          label: item,
                          value: item === '全部' ? null : item,
                        }))
                      "
                      style="width: 120px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      @update:value="(v) => handleCarOptionChange('country', v)"
                    />
                    <n-select
                      v-model:value="carQueryItems.vehicle_type"
                      placeholder="车型分类"
                      :options="vehicleTypeOptions"
                      style="width: 140px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @update:value="(v) => handleCarOptionChange('vehicle_type', v)"
                    />
                    <n-select
                      v-model:value="carQueryItems.brand"
                      placeholder="品牌"
                      :options="brandOptions"
                      style="width: 140px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @update:value="(v) => handleCarOptionChange('brand', v)"
                    />
                    <n-select
                      v-model:value="carQueryItems.series"
                      placeholder="车系"
                      :options="seriesOptions"
                      style="width: 140px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @update:value="(v) => handleCarOptionChange('series', v)"
                    />
                    <n-select
                      v-model:value="carQueryItems.model_year"
                      placeholder="年款"
                      :options="yearOptions"
                      style="width: 100px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @update:value="(v) => handleCarOptionChange('model_year', v)"
                    />
                  </div>
                  <div class="car-search-row">
                    <n-select
                      v-model:value="carQueryItems.displacement"
                      placeholder="排量"
                      :options="displacementOptions"
                      style="width: 120px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @update:value="(v) => handleCarOptionChange('displacement', v)"
                    />
                    <n-select
                      v-model:value="carQueryItems.trans_type"
                      placeholder="变速器类型"
                      :options="transOptions"
                      style="width: 140px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @update:value="(v) => handleCarOptionChange('trans_type', v)"
                    />
                    <n-select
                      v-model:value="carQueryItems.fuel_type"
                      placeholder="燃油类型"
                      :options="fuelTypeOptions"
                      style="width: 120px"
                      clearable
                      filterable
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @update:value="(v) => handleCarOptionChange('fuel_type', v)"
                    />
                    <n-button
                      type="primary"
                      :loading="searchLoading"
                      :disabled="!isCountrySelected"
                      @click="handleSearchClick"
                    >
                      搜索
                    </n-button>
                    <n-button
                      :loading="carOptionsLoading"
                      :disabled="!isCountrySelected"
                      @click="resetCarQuery"
                    >
                      重置
                    </n-button>
                  </div>
                </div>
              </template>
              <!-- Reach -->
              <n-input
                v-if="searchType === 'Reach'"
                v-model:value="reachSearchValue"
                placeholder="请输入Reach编号"
                clearable
                style="width: 540px"
              >
                <template #suffix>
                  <n-button type="primary" :loading="searchLoading" @click="handleSearchClick"
                    >搜索</n-button
                  >
                </template>
              </n-input>
              <!-- Reference -->
              <n-input
                v-if="searchType === 'Reference'"
                v-model:value="referenceSearchValue"
                placeholder="请输入参考号"
                clearable
                style="width: 540px"
              >
                <template #suffix>
                  <n-button type="primary" :loading="searchLoading" @click="handleSearchClick"
                    >搜索</n-button
                  >
                </template>
              </n-input>
            </n-space>
          </div>
        </div>

        <!-- 搜索结果区域 - 放在搜索框下方，可以超出背景图片范围 -->
        <div
          v-if="
            vinSearchResult ||
            oeSearchResult ||
            carSearchResult ||
            reachSearchResult ||
            referenceSearchResult
          "
          class="search-results-overlay"
        >
          <!-- VIN搜索结果美化展示 -->
          <n-card
            v-if="searchType === 'VIN' && vinSearchResult"
            class="vin-result-card"
            title="VIN搜索结果"
          >
            <div v-if="vinSearchResult?.data?.car_info" class="vin-info-header">
              <div class="vin-info-main">
                <span class="vin-info-brand">{{ vinSearchResult.data.car_info.brand }}</span>
                <span class="vin-info-model">{{ vinSearchResult.data.car_info.models }}</span>
                <span class="vin-info-year">{{ vinSearchResult.data.car_info.produced_year }}</span>
                <span class="vin-info-vin">VIN: {{ currentVinValue }}</span>
              </div>
              <div class="vin-info-sub">
                <span>制造商: {{ vinSearchResult.data.car_info.manufacturers }}</span>
                <span>车系: {{ vinSearchResult.data.car_info.series }}</span>
                <span>底盘号: {{ vinSearchResult.data.car_info.chassis_code }}</span>
              </div>
            </div>
            <n-descriptions
              v-if="vinSearchResult?.data?.car_info"
              :column="2"
              size="small"
              class="vin-desc"
            >
              <n-descriptions-item label="发动机型号">{{
                vinSearchResult.data.car_info.engine_model
              }}</n-descriptions-item>
              <n-descriptions-item label="排量">{{
                vinSearchResult.data.car_info.displacement
              }}</n-descriptions-item>
              <n-descriptions-item label="燃料类型">{{
                vinSearchResult.data.car_info.fuel_type
              }}</n-descriptions-item>
              <n-descriptions-item label="变速箱">{{
                vinSearchResult.data.car_info.transmission_description
              }}</n-descriptions-item>
              <n-descriptions-item label="驱动方式">{{
                vinSearchResult.data.car_info.drive_mode
              }}</n-descriptions-item>
            </n-descriptions>
            <n-divider
              v-if="
                vinSearchResult?.data?.oe_info?.length || vinSearchResult?.data?.reach_info?.length
              "
            />
            <n-card
              v-if="vinSearchResult?.data?.oe_info?.length"
              title="OE信息"
              size="small"
              class="vin-sub-card"
            >
              <n-data-table
                :columns="oeColumns"
                :data="vinSearchResult.data.oe_info"
                :pagination="{ pageSize: 10 }"
                size="small"
              />
            </n-card>
            <n-card
              v-if="vinSearchResult?.data?.reach_info?.length"
              title="Reach信息"
              size="small"
              class="vin-sub-card"
            >
              <n-data-table
                :columns="reachColumns"
                :data="vinSearchResult.data.reach_info"
                :pagination="{ pageSize: 10 }"
                size="small"
              />
            </n-card>
            <n-empty
              v-if="
                !vinSearchResult?.data?.car_info &&
                !vinSearchResult?.data?.oe_info?.length &&
                !vinSearchResult?.data?.reach_info?.length
              "
              description="暂无数据"
            />
          </n-card>

          <!-- OE搜索结果 -->
          <n-card
            v-if="searchType === 'OE' && oeSearchResult"
            title="OE搜索结果"
            class="oe-detail-card"
          >
            <!-- 基本信息 -->
            <div class="oe-basic-info-section">
              <n-grid :cols="24" :x-gap="24">
                <!-- 第一列 -->
                <n-gi :span="8">
                  <div class="info-item">
                    <div class="info-label">OE号*</div>
                    <div class="info-value">{{ oeSearchResult.data?.oe_base_info?.oe_number }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">OE名称</div>
                    <div class="info-value">{{ oeSearchResult.data?.oe_base_info?.oe_name }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">生产商</div>
                    <div class="info-value">
                      {{ oeSearchResult.data?.oe_base_info?.manufacturer }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">补充配件</div>
                    <div class="info-value">
                      <n-tag type="info" size="small">密封圈 (4)</n-tag>
                    </div>
                  </div>
                </n-gi>
                <!-- 第二列 -->
                <n-gi :span="8">
                  <div class="info-item">
                    <div class="info-label">材料*</div>
                    <div class="info-value">{{ oeSearchResult.data?.oe_base_info?.material }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">生产编号</div>
                    <div class="info-value">
                      {{ oeSearchResult.data?.oe_base_info?.production_number }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">用量</div>
                    <div class="info-value">{{ oeSearchResult.data?.oe_base_info?.quantity }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">位置</div>
                    <div class="info-value">{{ oeSearchResult.data?.oe_base_info?.position }}</div>
                  </div>
                </n-gi>
                <!-- 第三列 -->
                <n-gi :span="8">
                  <div class="info-item">
                    <div class="info-label">Project</div>
                    <div class="info-value">{{ oeSearchResult.data?.oe_base_info?.project }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">备注*</div>
                    <div class="info-value">{{ oeSearchResult.data?.oe_base_info?.remark }}</div>
                  </div>
                </n-gi>
              </n-grid>
            </div>

            <!-- 适用车型表格 -->
            <div class="car-table-section" style="margin-top: 20px">
              <div
                class="section-header"
                style="display: flex; align-items: center; margin-bottom: 12px"
              >
                <h3 style="margin: 0; font-size: 16px">
                  适用车型 (原始数据: {{ oeSearchResult.data?.car_info?.length || 0 }}条, 筛选后:
                  {{ filteredOeCarData.length }}条)
                </h3>
                <n-popover trigger="click" placement="bottom-start">
                  <template #trigger>
                    <span class="field-select-trigger">
                      <i
                        class="iconfont icon-menu"
                        style="font-size: 18px; vertical-align: middle; margin-right: 4px"
                        >≡</i
                      >
                      <span class="field-select-text">字段显示</span>
                    </span>
                  </template>
                  <n-checkbox-group v-model:value="carTableFields" class="field-checkbox-group">
                    <n-checkbox v-for="col in allCarColumns" :key="col.key" :value="col.key">
                      {{ col.title }}
                    </n-checkbox>
                  </n-checkbox-group>
                </n-popover>
              </div>

              <!-- 筛选工具栏 -->
              <div
                v-if="Object.keys(tableFilters).length > 0"
                class="filter-toolbar"
                style="margin-bottom: 12px"
              >
                <div class="filter-tags">
                  <span class="filter-label">当前筛选：</span>
                  <n-tag
                    v-for="tag in filterTags"
                    :key="tag.columnKey"
                    type="info"
                    closable
                    class="filter-tag"
                    @close="() => removeFilter(tag.columnKey)"
                  >
                    {{ tag.title }}: {{ tag.values.length }}项
                  </n-tag>
                  <n-button size="tiny" type="error" ghost @click="clearAllFilters">
                    清除所有筛选
                  </n-button>
                </div>
              </div>

              <div class="table-scroll-x">
                <n-data-table
                  :key="`oe-car-table-${tableRenderKey}`"
                  :columns="carTableColumns"
                  :data="filteredOeCarData"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                  :max-height="400"
                  :scroll-x="1200"
                  :allow-checking-not-loaded="true"
                  @update:filters="handleFiltersChange"
                />
                <n-empty
                  v-if="!oeSearchResult.data?.car_info?.length"
                  description="暂无适用车型数据"
                  style="margin: 20px 0"
                />
              </div>
            </div>

            <!-- 替代关系表格 -->
            <div class="replacement-table-section" style="margin-top: 20px">
              <div class="section-header" style="margin-bottom: 12px">
                <h3 style="margin: 0; font-size: 16px">替代关系</h3>
              </div>
              <n-data-table
                :columns="oeReplacementColumns"
                :data="oeSearchResult.data?.replacement_info || []"
                size="small"
                :max-height="400"
              />
            </div>

            <!-- Reach适配表格 -->
            <div class="reach-table-section" style="margin-top: 20px">
              <div class="section-header" style="margin-bottom: 12px">
                <h3 style="margin: 0; font-size: 16px">
                  Reach适配 ({{ oeSearchResult.data?.reach_info?.length || 0 }}条)
                </h3>
              </div>
              <n-data-table
                :columns="oeReachColumns"
                :data="oeSearchResult.data?.reach_info || []"
                size="small"
                :max-height="400"
                :scroll-x="600"
              />
              <n-empty
                v-if="!oeSearchResult.data?.reach_info?.length"
                description="暂无Reach适配数据"
                style="margin: 20px 0"
              />
            </div>
          </n-card>

          <!-- Car搜索结果 -->
          <n-card v-if="searchType === 'Car' && carSearchResult" title="车型搜索结果">
            <!-- 车型数据表格 -->
            <n-data-table
              :columns="carColumns"
              :data="carSearchResult?.data || []"
              :pagination="false"
              size="small"
              :max-height="400"
            />

            <!-- 分页 -->
            <n-pagination
              v-model:page="carPagination.page"
              v-model:page-size="carPagination.pageSize"
              :item-count="carPagination.itemCount"
              :page-sizes="carPagination.pageSizes"
              show-size-picker
              style="margin-top: 16px; justify-content: flex-end"
              @update:page="handleCarPageChange"
              @update:page-size="handleCarPageSizeChange"
            />

            <!-- 车型详情数据 -->
            <div v-if="carDetailData" class="car-detail-section">
              <n-divider />
              <n-card title="车型详情" size="small" class="detail-card">
                <n-descriptions bordered label-placement="left" :column="2" size="small">
                  <n-descriptions-item label="Reach" label-style="font-weight: bold;">
                    {{ carDetailData.reach_car_id || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="上市年份" label-style="font-weight: bold;">
                    {{ carDetailData.year_from || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="车型分类" label-style="font-weight: bold;">
                    {{ carDetailData.vehicle_type || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="停产年份" label-style="font-weight: bold;">
                    {{ carDetailData.year_till || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="厂家" label-style="font-weight: bold;">
                    {{ carDetailData.manufacturer || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="发动机型号" label-style="font-weight: bold;">
                    {{ carDetailData.engine_code || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="品牌" label-style="font-weight: bold;">
                    {{ carDetailData.brand || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="排量" label-style="font-weight: bold;">
                    {{ carDetailData.displacement || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="车系" label-style="font-weight: bold;">
                    {{ carDetailData.series || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="变速器描述" label-style="font-weight: bold;">
                    {{ carDetailData.trans_desc || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="车型" label-style="font-weight: bold;">
                    {{ carDetailData.model || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="排放标准" label-style="font-weight: bold;">
                    {{ carDetailData.emission_standard || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="驱动类型" label-style="font-weight: bold;">
                    {{ carDetailData.driver_type || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="燃油类型" label-style="font-weight: bold;">
                    {{ carDetailData.fuel_type || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="底盘号" label-style="font-weight: bold;">
                    {{ carDetailData.chassis || '-' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="VIO" label-style="font-weight: bold;">
                    {{ carDetailData.vio || '0' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="来源" label-style="font-weight: bold;">
                    {{ carDetailData.source || '-' }}
                  </n-descriptions-item>
                </n-descriptions>
              </n-card>
            </div>

            <!-- OE详情数据 -->
            <div v-if="carOeData.length > 0" class="oe-detail-section">
              <n-card title="OE详情" size="small" class="detail-card">
                <n-data-table
                  :columns="oeColumns"
                  :data="carOeData"
                  :max-height="300"
                  virtual-scroll
                  size="small"
                />
              </n-card>
            </div>
          </n-card>

          <!-- Reach搜索结果 -->

          <!-- 重新构建的n-card -->
          <n-card
            v-if="searchType === 'Reach' && reachSearchResult"
            :key="`reach-card-${tableRenderKey}`"
            class="reach-detail-card"
            style="margin-top: 20px"
          >
            <template #header>
              <div class="reach-header">
                <div class="reach-title-section">
                  <span class="reach-title">Reach搜索结果</span>
                  <!-- 可供状态标识 -->
                  <span
                    v-if="reachSearchResult?.data?.available"
                    class="available-status"
                    :class="{
                      available: reachSearchResult.data.available === '可供',
                      unavailable: reachSearchResult.data.available === '不可供',
                    }"
                  >
                    {{ reachSearchResult.data.available }}
                  </span>
                  <span
                    v-if="reachSearchResult?.data?.domestically"
                    class="available-status"
                    :class="{
                      available: reachSearchResult.data.available === '可供',
                      unavailable: reachSearchResult.data.available === '不可供',
                    }"
                  >
                    {{ reachSearchResult.data.domestically }}
                  </span>
                </div>
                <!-- 图片按钮区域 -->
                <div class="reach-image-buttons">
                  <n-space>
                    <n-button
                      type="primary"
                      size="small"
                      :disabled="!reachProductPhotoList.length"
                      @click="handleReachPhotoClick"
                    >
                      <template #icon>
                        <n-icon>
                          <svg viewBox="0 0 24 24">
                            <path
                              fill="currentColor"
                              d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"
                            />
                          </svg>
                        </n-icon>
                      </template>
                      产品实拍图
                    </n-button>
                    <n-button
                      type="primary"
                      size="small"
                      :disabled="!reachProductDiagram"
                      @click="handleReachDiagramClick"
                    >
                      <template #icon>
                        <n-icon>
                          <svg viewBox="0 0 24 24">
                            <path
                              fill="currentColor"
                              d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z"
                            />
                          </svg>
                        </n-icon>
                      </template>
                      产品简图
                    </n-button>
                  </n-space>
                </div>
              </div>
            </template>

            <!-- 产品基础信息 -->
            <div class="reach-basic-info-section">
              <n-descriptions
                :column="3"
                size="medium"
                bordered
                label-placement="left"
                class="reach-desc"
              >
                <n-descriptions-item
                  v-for="item in productInfoEntries"
                  :key="item.key"
                  :label="item.key"
                >
                  {{ item.value || '-' }}
                </n-descriptions-item>
              </n-descriptions>
            </div>

            <!-- 适用车型表格 -->
            <div class="car-table-section" style="margin-top: 20px">
              <div
                class="section-header"
                style="display: flex; align-items: center; margin-bottom: 12px"
              >
                <h3 style="margin: 0; font-size: 16px">
                  适用车型 (原始数据: {{ reachSearchResult.data?.car_info?.length || 0 }}条, 筛选后:
                  {{ filteredReachCarData.length }}条)
                </h3>
                <n-popover trigger="click" placement="bottom-start">
                  <template #trigger>
                    <span class="field-select-trigger">
                      <i
                        class="iconfont icon-menu"
                        style="font-size: 18px; vertical-align: middle; margin-right: 4px"
                        >≡</i
                      >
                      <span class="field-select-text">字段显示</span>
                    </span>
                  </template>
                  <n-checkbox-group v-model:value="carTableFields" class="field-checkbox-group">
                    <n-checkbox v-for="col in allCarColumns" :key="col.key" :value="col.key">
                      {{ col.title }}
                    </n-checkbox>
                  </n-checkbox-group>
                </n-popover>
              </div>

              <!-- 筛选工具栏 -->
              <div
                v-if="Object.keys(tableFilters).length > 0"
                class="filter-toolbar"
                style="margin-bottom: 12px"
              >
                <div class="filter-tags">
                  <span class="filter-label">当前筛选：</span>
                  <n-tag
                    v-for="tag in filterTags"
                    :key="tag.columnKey"
                    type="info"
                    closable
                    class="filter-tag"
                    @close="() => removeFilter(tag.columnKey)"
                  >
                    {{ tag.title }}: {{ tag.values.length }}项
                  </n-tag>
                  <n-button size="tiny" type="error" ghost @click="clearAllFilters">
                    清除所有筛选
                  </n-button>
                </div>
              </div>

              <div class="table-scroll-x">
                <n-data-table
                  :key="`reach-car-table-${tableRenderKey}`"
                  :columns="carTableColumns"
                  :data="filteredReachCarData"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                  :max-height="400"
                  :scroll-x="1200"
                  :allow-checking-not-loaded="true"
                  @update:filters="handleFiltersChange"
                />
              </div>
            </div>

            <!-- Reach适配表格 -->
            <div class="reach-table-section" style="margin-top: 20px">
              <div class="section-header" style="margin-bottom: 12px">
                <h3 style="margin: 0; font-size: 16px">
                  Reach适配 ({{ reachSearchResult.data?.reach_info?.length || 0 }}条)
                </h3>
              </div>
              <n-data-table
                :columns="reachTableColumns"
                :data="reachSearchResult.data?.reach_info || []"
                size="small"
                :max-height="400"
                :scroll-x="600"
              />
            </div>
          </n-card>

          <!-- Reference搜索结果 -->
          <n-card
            v-if="searchType === 'Reference' && referenceSearchResult"
            title="参考号搜索结果"
            class="reference-result-card"
          >
            <n-data-table
              :columns="referenceColumns"
              :data="referenceSearchResult?.data || []"
              :pagination="{ pageSize: 10 }"
              size="small"
              :max-height="400"
              :row-props="getReferenceRowProps"
              class="reference-table"
            />
            <n-empty
              v-if="!referenceSearchResult?.data?.length"
              description="暂无数据"
              style="margin: 20px 0"
            />
          </n-card>
        </div>

        <!-- 车型选择弹窗 -->
        <n-modal
          v-model:show="showCarSelectionModal"
          preset="card"
          title="请选择车型"
          style="width: 1200px"
          :mask-closable="false"
        >
          <div class="car-selection-content">
            <p class="car-selection-tip">
              检测到VIN码对应多个车型，请点击选择正确的车型以获取详细信息：
            </p>
            <n-data-table
              :columns="carSelectionColumns"
              :data="vinCarInfoList"
              :pagination="{ pageSize: 10 }"
              size="small"
              :scroll-x="1200"
              :row-props="getCarRowProps"
              class="car-selection-table"
            />
          </div>
          <template #footer>
            <div class="car-selection-footer">
              <n-button @click="handleCancelCarSelection">取消</n-button>
            </div>
          </template>
        </n-modal>

        <!-- 图片预览弹窗 -->
        <n-modal
          v-model:show="showImagePreview"
          :mask-closable="false"
          :closable="false"
          :style="{ width: '95vw', height: '95vh' }"
          class="image-preview-modal"
        >
          <div class="image-preview-wrapper">
            <!-- 头部工具栏 -->
            <div class="preview-header">
              <div class="preview-title">
                <span>图片预览</span>
                <span v-if="previewImageList.length > 1" class="preview-counter">
                  {{ currentPreviewIndex + 1 }} / {{ previewImageList.length }}
                </span>
              </div>
              <div class="preview-controls">
                <n-button
                  v-if="previewImageList.length > 1"
                  size="small"
                  type="primary"
                  ghost
                  @click="switchPreviewImage('prev')"
                >
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"
                      />
                    </svg>
                  </n-icon>
                  上一张
                </n-button>
                <n-button
                  v-if="previewImageList.length > 1"
                  size="small"
                  type="primary"
                  ghost
                  @click="switchPreviewImage('next')"
                >
                  下一张
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
                      />
                    </svg>
                  </n-icon>
                </n-button>
                <n-button size="small" ghost @click="resetImageState">
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M18,11H13L14.5,9.5L13.08,8.08L9.16,12L13.08,15.92L14.5,14.5L13,13H18V11Z"
                      />
                    </svg>
                  </n-icon>
                  重置
                </n-button>
                <n-button size="small" type="error" ghost @click="closeImagePreview">
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
                      />
                    </svg>
                  </n-icon>
                  关闭
                </n-button>
              </div>
            </div>

            <!-- 图片容器 -->
            <div
              class="image-preview-container"
              @wheel="handleImageWheel"
              @mousemove="handleImageMouseMove"
              @mouseup="handleImageMouseUp"
              @mouseleave="handleImageMouseUp"
            >
              <img
                :src="previewImageUrl"
                alt="图片预览"
                class="preview-image"
                :style="{
                  transform: `scale(${imageScale}) translate(${imagePosition.x / imageScale}px, ${
                    imagePosition.y / imageScale
                  }px)`,
                  cursor: imageScale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default',
                }"
                @mousedown="handleImageMouseDown"
                @dragstart.prevent
              />
            </div>

            <!-- 底部提示 -->
            <div class="preview-footer">
              <div class="preview-tips">
                <span>💡 快捷键：</span>
                <span>滚轮/±键缩放 ({{ Math.round(imageScale * 100) }}%)</span>
                <span v-if="imageScale > 1">• 拖拽移动</span>
                <span v-if="previewImageList.length > 1">• ←→切换图片</span>
                <span>• 0键重置 • ESC关闭</span>
              </div>
            </div>
          </div>
        </n-modal>
      </div>
    </n-spin>
  </CommonPage>
</template>

<script setup lang="ts">
import { ref, h, computed, onMounted, nextTick, watch, onBeforeUnmount, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import {
  NCard,
  NSpace,
  NInput,
  NButton,
  NSelect,
  NDescriptions,
  NDescriptionsItem,
  NDataTable,
  NEmpty,
  NDivider,
  NSpin,
  NGrid,
  NGi,
  NTag,
  NPopover,
  NCheckboxGroup,
  NCheckbox,
  NPagination,
  NModal,
  NIcon,
} from 'naive-ui'
import api from '@/api'
import CommonPage from '@/components/page/CommonPage.vue'

defineOptions({ name: '数据查询搜索' })

// 获取路由信息
const route = useRoute()

// 搜索类型
const searchType = ref('VIN')

// VIN搜索相关
const vinSearchValue = ref('')
const vinSearchResult = ref(null)
const vinCarInfoList = ref([]) // VIN搜索返回的车型信息列表
const showCarSelectionModal = ref(false) // 车型选择弹窗显示状态
const selectedCarInfo = ref(null) // 用户选择的车型信息
const vinOeReachData = ref(null) // 通过getOEReachByVin获取的OE和Reach数据
const currentVinValue = ref('') // 当前搜索的VIN值，用于展示

// OE搜索相关
const oeSearchValue = ref('')
const oeSearchResult = ref(null)

// Car搜索相关
const carSearchResult = ref(null)
const carDetailData = ref(null) // 车型详情数据
const carOeData = ref([]) // 车型对应的OE数据

// 车型查询条件（与车型数据页面保持一致）
const carQueryItems = ref({
  country: null,
  vehicle_type: null,
  brand: null,
  series: null,
  model_year: null,
  displacement: null,
  trans_type: null,
  fuel_type: null,
})

// 地区选项（参照 cars/index.vue）
const countryOptions = ref(['中国', '北美', '欧洲', '俄罗斯', '加拿大', '墨西哥', '东南亚'])

// 车型搜索分页（参照 cars/index.vue）
const carPagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})

// 车型查询选项
const vehicleTypeOptions = ref([])
const brandOptions = ref([])
const seriesOptions = ref([])
const yearOptions = ref([])
const displacementOptions = ref([])
const transOptions = ref([])
const fuelTypeOptions = ref([])

// Reach搜索相关
const reachSearchValue = ref('')
const reachSearchResult = ref(null)

// Reference搜索相关
const referenceSearchValue = ref('')
const referenceSearchResult = ref(null)

// 图片预览相关状态
const showImagePreview = ref(false)
const previewImageUrl = ref('')
const previewImageList = ref([])
const currentPreviewIndex = ref(0)
const imageScale = ref(1)
const imagePosition = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })

// 搜索处理函数
const searchLoading = ref(false)

// 计算属性：判断是否选择了地区
const isCountrySelected = computed(() => {
  return carQueryItems.value.country !== null && carQueryItems.value.country !== undefined
})

// 获取OE和Reach数据
const fetchOEReachData = async (carInfo) => {
  try {
    const response = await api.getOEReachByVin({
      vin: currentVinValue.value,
      code: carInfo.code,
    })
    vinOeReachData.value = response
    // 构建完整的搜索结果
    vinSearchResult.value = {
      data: {
        car_info: carInfo,
        oe_info: response?.data?.oe_info || [],
        reach_info: response?.data?.reach_info || [],
      },
    }
  } catch (error) {
    console.error('获取OE和Reach数据失败:', error)
    vinOeReachData.value = null
    // 即使获取OE和Reach数据失败，也要显示车型信息
    vinSearchResult.value = {
      data: {
        car_info: carInfo,
        oe_info: [],
        reach_info: [],
      },
    }
  }
}

const handleSearch = async (isPagination = false) => {
  searchLoading.value = true
  try {
    let response
    switch (searchType.value) {
      case 'VIN':
        // 保存当前搜索的VIN值用于展示
        currentVinValue.value = vinSearchValue.value
        // 第一步：只获取车型数据
        response = await api.searchVin({ vin: vinSearchValue.value })
        if (response && response.data && response.data.car_info) {
          const carInfoList = Array.isArray(response.data.car_info)
            ? response.data.car_info
            : [response.data.car_info]

          vinCarInfoList.value = carInfoList

          if (carInfoList.length > 1) {
            // 多条数据，显示选择弹窗
            showCarSelectionModal.value = true
            // 清空之前的结果
            vinSearchResult.value = null
            vinOeReachData.value = null
          } else if (carInfoList.length === 1) {
            // 只有一条数据，直接获取OE和Reach数据
            selectedCarInfo.value = carInfoList[0]
            await fetchOEReachData(carInfoList[0])
          } else {
            // 没有数据
            vinSearchResult.value = response
            vinOeReachData.value = null
          }
        } else {
          vinSearchResult.value = response
          vinOeReachData.value = null
        }
        break

      case 'OE':
        response = await api.searchOE({ oe: oeSearchValue.value })
        oeSearchResult.value = response
        break

      case 'Car':
        // 如果是新搜索（不是分页），重置到第一页
        if (!isPagination) {
          carPagination.value.page = 1
        }
        // 清空车型详情和OE详情数据
        carDetailData.value = null
        carOeData.value = []
        // 使用 getCarsList API 进行车型搜索
        response = await api.getCarsList({
          ...carQueryItems.value,
          page: carPagination.value.page,
          pageSize: carPagination.value.pageSize,
        })
        carSearchResult.value = response
        // 更新分页信息
        if (response && response.total !== undefined) {
          carPagination.value.itemCount = response.total
        }
        break

      case 'Reach':
        console.log('🔍 开始Reach搜索:', reachSearchValue.value)
        response = await api.searchReach({ reach: reachSearchValue.value })
        console.log('📦 Reach搜索API响应:', response)
        console.log('📊 Reach搜索数据结构:', {
          hasData: !!response?.data,
          dataKeys: response?.data ? Object.keys(response.data) : [],
          carInfoType: typeof response?.data?.car_info,
          carInfoLength: Array.isArray(response?.data?.car_info)
            ? response.data.car_info.length
            : 'not array',
          carInfoSample:
            Array.isArray(response?.data?.car_info) && response.data.car_info.length > 0
              ? response.data.car_info[0]
              : 'no data',
        })
        reachSearchResult.value = response

        // 检查并修复数据结构
        checkAndFixReachSearchResult()

        // 多层次强制触发响应式更新
        nextTick(() => {
          console.log('🔄 第一次更新：修复数据结构后')
          forceUpdateColumns.value++
          tableRenderKey.value++

          // 第二次更新：确保计算属性重新计算
          nextTick(() => {
            console.log('🔄 第二次更新：强制计算属性重新计算')
            forceUpdateColumns.value++

            // 第三次更新：最终确保表格渲染
            setTimeout(() => {
              console.log('🔄 第三次更新：最终表格渲染')
              tableRenderKey.value++
              forceUpdateColumns.value++

              // 最终数据检查
              console.log('📊 最终Reach数据检查:', {
                hasReachResult: !!reachSearchResult.value,
                carInfoLength: reachSearchResult.value?.data?.car_info?.length || 0,
                reachInfoLength: reachSearchResult.value?.data?.reach_info?.length || 0,
                filteredCarDataLength: filteredReachCarData.value?.length || 0,
                columnsLength: carTableColumns.value?.length || 0,
              })
            }, 100)
          })
        })
        break

      case 'Reference':
        response = await api.searchReference({ reference: referenceSearchValue.value })
        referenceSearchResult.value = response
        break
    }

    // 搜索完成后，确保表格正确渲染
    if (response && (searchType.value === 'OE' || searchType.value === 'Reach')) {
      console.log('🔍 搜索完成，更新表格状态:', {
        searchType: searchType.value,
        hasData: !!response.data,
        carInfoLength: response.data?.car_info?.length || 0,
      })

      // 使用多重nextTick确保DOM完全更新
      nextTick(() => {
        nextTick(() => {
          resetTableFields()
          // 额外的强制更新
          setTimeout(() => {
            forceUpdateColumns.value++
            tableRenderKey.value++
          }, 100)
        })
      })
    }
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    searchLoading.value = false
  }
}

// 车型表格列定义（参照 cars/index.vue）
const carColumns = [
  {
    title: '品牌',
    key: 'brand',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '厂家',
    key: 'manufacturer',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型分类',
    key: 'vehicle_type',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系',
    key: 'series',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型',
    key: 'model',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '年款',
    key: 'model_year',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '排量',
    key: 'displacement',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Vio',
    key: 'vio',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '更多',
    key: 'actions',
    align: 'center',
    width: 'auto',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => handleCarDetail(row),
        },
        { default: () => '详情' }
      )
    },
  },
]

// OE表格列定义（参照 cars/index.vue 的 oecolumns）
const oeColumns = [
  {
    title: 'Reach号/主号',
    key: 'group_number',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
    render(row) {
      return h(
        'a',
        {
          style: 'color: #10b981; cursor: pointer;',
          onClick: () => handleGroupClick(row.group_number),
        },
        row.group_number
      )
    },
  },
  {
    title: '可供状态',
    key: 'available',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'OE/主号',
    key: 'PART_NUMBER',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
    render(row) {
      return h(
        'a',
        {
          style: 'color: #3b82f6; cursor: pointer;',
          onClick: () => handleOeClick(row.PART_NUMBER),
        },
        row.PART_NUMBER
      )
    },
  },
  {
    title: '英文名称',
    key: 'PART_TERMINOLOGY_NAME',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '标准名称',
    key: 'standard_label',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
]

// Reach表格列定义
const reachColumns = [
  { title: '物料号', key: 'Materiel_Number' },
  { title: '物料名称', key: 'Materiel_Name' },
  { title: '芯体长(mm)', key: '芯体长mm' },
  { title: '芯体宽(mm)', key: '芯体宽mm' },
  { title: '芯体厚(mm)', key: '芯体厚mm' },
  { title: '材质', key: '材质' },
  { title: '结构', key: '结构' },
  { title: '进口管位置', key: '进口管位置' },
  { title: '出口管位置', key: '出口管位置' },
]

// Reference表格列定义
const referenceColumns = [
  {
    title: '厂商',
    key: 'Type',
    resizable: true,
    align: 'center',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: '厂商号',
    key: 'Brand_Number',
    resizable: true,
    align: 'center',
    width: 150,
    ellipsis: { tooltip: true },
  },
  {
    title: 'Reach号',
    key: 'Reach_Number',
    resizable: true,
    align: 'center',
    width: 150,
    ellipsis: { tooltip: true },
    render(row) {
      return h(
        'a',
        {
          style: 'color: #10b981; cursor: pointer;',
          onClick: () => handleReferenceReachClick(row.Reach_Number),
        },
        row.Reach_Number
      )
    },
  },
  {
    title: '可供状态',
    key: 'Available',
    resizable: true,
    align: 'center',
    width: 120,
    ellipsis: { tooltip: true },
  },
]

// 处理车型详情点击（参照 cars/index.vue 的 Detail 函数）
const handleCarDetail = async (rowData) => {
  carDetailData.value = rowData // 将当前行数据存储到 carDetailData 中
  try {
    const oe_response = await api.getOeDataList({ reach_car_id: rowData.reach_car_id })
    carOeData.value = [...(oe_response.data || [])]
  } catch (error) {
    carOeData.value = []
  }
}

function handleOeClick(oe) {
  console.log('🔍 点击OE号进行搜索:', oe)

  // 只清空当前搜索类型的结果
  oeSearchResult.value = null

  // 设置搜索类型和值
  searchType.value = 'OE'
  oeSearchValue.value = oe

  // 重置表格状态
  nextTick(() => {
    resetTableFields()
    handleSearch(false) // 新搜索
  })
}

function handleGroupClick(group) {
  console.log('🔍 点击Reach号进行搜索:', group)

  // 只清空当前搜索类型的结果
  reachSearchResult.value = null

  // 设置搜索类型和值
  searchType.value = 'Reach'
  reachSearchValue.value = group

  // 重置表格状态
  nextTick(() => {
    resetTableFields()
    handleSearch(false) // 新搜索
  })
}

// 处理参考号搜索结果中的Reach号点击
function handleReferenceReachClick(reachNumber) {
  console.log('🔍 从参考号搜索结果点击Reach号进行搜索:', reachNumber)

  // 只清空当前搜索类型的结果
  reachSearchResult.value = null

  // 设置搜索类型和值
  searchType.value = 'Reach'
  reachSearchValue.value = reachNumber

  // 重置表格状态
  nextTick(() => {
    resetTableFields()
    handleSearch(false) // 新搜索
  })
}

// 参考号表格行属性
const getReferenceRowProps = (row) => {
  return {
    style: 'cursor: pointer;',
    onClick: () => handleReferenceReachClick(row.Reach_Number),
  }
}

// 图片点击处理方法
const handleImageClick = (imageUrl, imageList = null, index = 0) => {
  previewImageUrl.value = imageUrl
  previewImageList.value = imageList || [imageUrl]
  currentPreviewIndex.value = index
  imageScale.value = 1
  imagePosition.value = { x: 0, y: 0 }
  showImagePreview.value = true
  // 添加键盘监听器
  nextTick(() => {
    document.addEventListener('keydown', handleKeyDown)
  })
}

// Reach产品实拍图按钮点击
const handleReachPhotoClick = () => {
  if (reachProductPhotoList.value.length > 0) {
    handleImageClick(reachProductPhotoList.value[0], reachProductPhotoList.value, 0)
  }
}

// Reach产品简图按钮点击
const handleReachDiagramClick = () => {
  if (reachProductDiagram.value) {
    handleImageClick(reachProductDiagram.value)
  }
}

// 预览图片切换
const switchPreviewImage = (direction) => {
  if (previewImageList.value.length <= 1) return

  if (direction === 'prev') {
    currentPreviewIndex.value =
      currentPreviewIndex.value > 0
        ? currentPreviewIndex.value - 1
        : previewImageList.value.length - 1
  } else {
    currentPreviewIndex.value =
      currentPreviewIndex.value < previewImageList.value.length - 1
        ? currentPreviewIndex.value + 1
        : 0
  }

  previewImageUrl.value = previewImageList.value[currentPreviewIndex.value]
  // 切换图片时重置缩放和位置
  imageScale.value = 1
  imagePosition.value = { x: 0, y: 0 }
}

// 图片缩放
const handleImageWheel = (event) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.max(0.5, Math.min(3, imageScale.value + delta))
  imageScale.value = newScale

  // 如果缩放到1，重置位置
  if (newScale === 1) {
    imagePosition.value = { x: 0, y: 0 }
  }
}

// 图片拖拽开始
const handleImageMouseDown = (event) => {
  if (imageScale.value <= 1) return
  isDragging.value = true
  dragStart.value = {
    x: event.clientX - imagePosition.value.x,
    y: event.clientY - imagePosition.value.y,
  }
  event.preventDefault()
}

// 图片拖拽移动
const handleImageMouseMove = (event) => {
  if (!isDragging.value || imageScale.value <= 1) return
  imagePosition.value = {
    x: event.clientX - dragStart.value.x,
    y: event.clientY - dragStart.value.y,
  }
}

// 图片拖拽结束
const handleImageMouseUp = () => {
  isDragging.value = false
}

// 重置图片状态
const resetImageState = () => {
  imageScale.value = 1
  imagePosition.value = { x: 0, y: 0 }
  isDragging.value = false
}

// 关闭预览时重置状态
const closeImagePreview = () => {
  showImagePreview.value = false
  resetImageState()
  document.removeEventListener('keydown', handleKeyDown)
}

// 键盘事件处理
const handleKeyDown = (event) => {
  if (!showImagePreview.value) return

  switch (event.key) {
    case 'Escape':
      closeImagePreview()
      break
    case 'ArrowLeft':
      event.preventDefault()
      switchPreviewImage('prev')
      break
    case 'ArrowRight':
      event.preventDefault()
      switchPreviewImage('next')
      break
    case '=':
    case '+':
      event.preventDefault()
      imageScale.value = Math.min(3, imageScale.value + 0.2)
      break
    case '-':
      event.preventDefault()
      imageScale.value = Math.max(0.5, imageScale.value - 0.2)
      break
    case '0':
      event.preventDefault()
      resetImageState()
      break
  }
}

// 获取车型查询选项
const fetchCarOptions = async () => {
  try {
    const apiOptions = await api.getOptionList()
    vehicleTypeOptions.value = apiOptions.data.vehicle_type || []
    brandOptions.value = apiOptions.data.brand || []
    seriesOptions.value = apiOptions.data.series || []
    yearOptions.value = apiOptions.data.model_year || []
    displacementOptions.value = apiOptions.data.displacement || []
    transOptions.value = apiOptions.data.trans_type || []
    fuelTypeOptions.value = apiOptions.data.fuel_type || []
  } catch (error) {
    console.error('获取车型选项失败', error)
  }
}

// 处理车型选项变化（带联动逻辑）
const carOptionsLoading = ref(false)
let optionChangeTimeout = null

const handleCarOptionChange = async (optionKey, selectedValue) => {
  // 清除之前的定时器，实现防抖
  if (optionChangeTimeout) {
    clearTimeout(optionChangeTimeout)
  }

  // 立即更新选中的值
  carQueryItems.value[optionKey] = selectedValue

  // 如果是地区选择且清空了地区，则清空其他所有条件
  if (optionKey === 'country' && (selectedValue === null || selectedValue === undefined)) {
    carQueryItems.value = {
      country: null,
      vehicle_type: null,
      brand: null,
      series: null,
      model_year: null,
      displacement: null,
      trans_type: null,
      fuel_type: null,
    }
  }

  // 设置防抖定时器
  optionChangeTimeout = setTimeout(async () => {
    carOptionsLoading.value = true
    try {
      // 从后端获取并更新其他选项（联动逻辑）
      const updatedOptions = await api.getOptionList(carQueryItems.value)

      // 更新所有下拉框选项
      vehicleTypeOptions.value = updatedOptions.data.vehicle_type || []
      brandOptions.value = updatedOptions.data.brand || []
      seriesOptions.value = updatedOptions.data.series || []
      yearOptions.value = updatedOptions.data.model_year || []
      displacementOptions.value = updatedOptions.data.displacement || []
      transOptions.value = updatedOptions.data.trans_type || []
      fuelTypeOptions.value = updatedOptions.data.fuel_type || []
    } catch (error) {
      console.error('处理车型选项变化失败', error)
    } finally {
      carOptionsLoading.value = false
    }
  }, 300) // 300ms 防抖延迟
}

// 重置车型查询条件
const resetCarQuery = async () => {
  // 重置查询条件
  carQueryItems.value = {
    country: null,
    vehicle_type: null,
    brand: null,
    series: null,
    model_year: null,
    displacement: null,
    trans_type: null,
    fuel_type: null,
  }

  // 重置分页
  carPagination.value.page = 1
  carPagination.value.itemCount = 0

  // 清空车型详情和OE详情数据
  carDetailData.value = null
  carOeData.value = []

  // 重新获取所有选项
  await fetchCarOptions()
}

// 包装搜索函数，用于按钮点击
const handleSearchClick = () => {
  handleSearch(false) // 新搜索
}

// 处理车型选择
const handleCarSelection = async (carInfo) => {
  selectedCarInfo.value = carInfo
  showCarSelectionModal.value = false
  searchLoading.value = true
  try {
    await fetchOEReachData(carInfo)
  } finally {
    searchLoading.value = false
  }
}

// 取消车型选择
const handleCancelCarSelection = () => {
  showCarSelectionModal.value = false
  vinCarInfoList.value = []
  selectedCarInfo.value = null
}

// 获取表格行属性，添加点击事件
const getCarRowProps = (row) => {
  return {
    style: 'cursor: pointer;',
    onClick: () => handleCarSelection(row),
  }
}

// 车型搜索分页处理函数（参照 cars/index.vue）
const handleCarPageChange = (page) => {
  carPagination.value.page = page
  if (searchType.value === 'Car') {
    handleSearch(true) // 分页搜索
  }
}

const handleCarPageSizeChange = (pageSize) => {
  carPagination.value.pageSize = pageSize
  carPagination.value.page = 1
  if (searchType.value === 'Car') {
    handleSearch(true) // 分页搜索
  }
}

// 表格重新渲染key
const tableRenderKey = ref(0)

// 表格筛选状态
const tableFilters = ref({})

// 计算属性：格式化筛选标签数据
const filterTags = computed(() => {
  const filters = tableFilters.value
  return Object.keys(filters).map((columnKey) => ({
    columnKey,
    values: Array.isArray(filters[columnKey]) ? filters[columnKey] : [],
    title: allCarColumns.find((col) => col.key === columnKey)?.title || columnKey,
  }))
})

// 应用筛选条件到数据
const applyFiltersToData = (data, filters) => {
  if (!data || !Array.isArray(data) || Object.keys(filters).length === 0) return data

  return data.filter((row) => {
    return Object.entries(filters).every(([columnKey, selectedValues]) => {
      // 确保 selectedValues 是字符串数组
      const filterValues = Array.isArray(selectedValues) ? selectedValues : []
      if (!filterValues || filterValues.length === 0) return true

      const cellValue = row[columnKey]
      const cellValueStr =
        cellValue !== null && cellValue !== undefined ? String(cellValue).trim() : ''

      // 调试特殊字符筛选
      const hasSpecialChars =
        cellValueStr.includes('/') || cellValueStr.includes('(') || cellValueStr.includes(')')
      if (hasSpecialChars) {
        const result = filterValues.includes(cellValueStr)
        console.log(`🔍 数据筛选调试 ${columnKey}:`, {
          筛选条件: filterValues,
          行数据值: `"${cellValueStr}"`,
          匹配结果: result,
          严格匹配: filterValues.some((v) => v === cellValueStr),
        })
      }

      return filterValues.includes(cellValueStr)
    })
  })
}

// 计算属性：筛选后的OE车型数据
const filteredOeCarData = computed(() => {
  // 强制依赖forceUpdateColumns来触发重新计算
  forceUpdateColumns.value

  const data = oeSearchResult.value?.data?.car_info || []
  console.log('🔄 重新计算filteredOeCarData:', {
    searchType: searchType.value,
    rawDataLength: data.length,
    forceUpdateColumns: forceUpdateColumns.value,
    hasFilters: Object.keys(tableFilters.value).length > 0,
  })

  return applyFiltersToData(data, tableFilters.value)
})

// 计算属性：筛选后的Reach车型数据
const filteredReachCarData = computed(() => {
  // 强制依赖forceUpdateColumns来触发重新计算
  forceUpdateColumns.value

  const data = reachSearchResult.value?.data?.car_info || []
  console.log('🔄 重新计算filteredReachCarData:', {
    searchType: searchType.value,
    rawDataLength: data.length,
    forceUpdateColumns: forceUpdateColumns.value,
    hasFilters: Object.keys(tableFilters.value).length > 0,
  })

  return applyFiltersToData(data, tableFilters.value)
})

// 获取列的唯一值用于筛选
const getColumnUniqueValues = (data, columnKey) => {
  if (!data || !Array.isArray(data)) return []
  const values = data
    .map((row) => row[columnKey])
    .filter((val) => val !== null && val !== undefined && val !== '')
    .map((val) => String(val).trim()) // 确保所有值都是字符串类型并去除空格
  return [...new Set(values)].sort()
}

// 处理筛选变化
const handleFiltersChange = (filters) => {
  console.log('筛选变化:', filters)
  console.log('筛选变化详情:', JSON.stringify(filters, null, 2))
  // 只保留有实际筛选条件的字段
  const activeFilters = {}
  Object.keys(filters).forEach((key) => {
    if (filters[key] && filters[key].length > 0) {
      activeFilters[key] = filters[key]
      console.log(`字段 ${key} 筛选条件:`, filters[key])
    }
  })
  console.log('活跃筛选条件:', activeFilters)
  tableFilters.value = activeFilters
}

// 移除单个筛选条件
const removeFilter = (columnKey) => {
  delete tableFilters.value[columnKey]
  tableRenderKey.value++
}

// 清除所有筛选
const clearAllFilters = () => {
  tableFilters.value = {}
  tableRenderKey.value++
}

// 重置表格字段选择和强制重新渲染
const resetTableFields = () => {
  // 清空筛选条件
  tableFilters.value = {}

  // 重新初始化表格字段
  carTableFields.value = initCarTableFields()

  // 强制重新渲染表格
  tableRenderKey.value++

  // 强制更新计算属性
  forceUpdateColumns.value++

  console.log('🔄 表格字段重置完成:', {
    searchType: searchType.value,
    carTableFields: carTableFields.value,
    tableRenderKey: tableRenderKey.value,
    forceUpdateColumns: forceUpdateColumns.value,
    hasOeData: !!oeSearchResult.value?.data?.car_info?.length,
    hasReachData: !!reachSearchResult.value?.data?.car_info?.length,
  })
}

// 检查并修复Reach搜索结果数据结构
const checkAndFixReachSearchResult = () => {
  if (!reachSearchResult.value || !reachSearchResult.value.data) {
    console.log('⚠️ Reach搜索结果为空，无需修复')
    return
  }

  console.log('🔧 检查Reach搜索结果数据结构')

  // 创建一个新的响应式对象，确保Vue能正确检测变化
  const originalData = reachSearchResult.value.data
  const fixedData = {
    ...originalData,
    car_info: Array.isArray(originalData.car_info)
      ? originalData.car_info
      : originalData.car_info
      ? [originalData.car_info]
      : [],
    reach_info: Array.isArray(originalData.reach_info)
      ? originalData.reach_info
      : originalData.reach_info
      ? [originalData.reach_info]
      : [],
  }

  // 完全重新赋值，确保Vue响应式系统能检测到变化
  reachSearchResult.value = {
    ...reachSearchResult.value,
    data: fixedData,
  }

  console.log('✅ Reach搜索结果数据结构修复完成:', {
    carInfoLength: fixedData.car_info.length,
    reachInfoLength: fixedData.reach_info.length,
  })
}

// 初始化组件状态
const initializeComponent = async () => {
  console.log('🚀 初始化查询页面组件')

  // 清空所有搜索结果（保留已有的搜索结果，只在必要时清空）
  // vinSearchResult.value = null
  // oeSearchResult.value = null
  // carSearchResult.value = null
  // reachSearchResult.value = null
  // referenceSearchResult.value = null

  // 重置表格状态
  tableFilters.value = {}
  carTableFields.value = initCarTableFields()
  tableRenderKey.value++

  // 获取车型选项
  await fetchCarOptions()

  console.log('✅ 查询页面组件初始化完成')
}

// 组件挂载时获取车型选项和初始化表格
onMounted(async () => {
  console.log('🔧 组件挂载，开始初始化')
  await initializeComponent()

  // 监听窗口大小变化，重新初始化表格
  const handleResize = () => {
    nextTick(() => {
      resetTableFields()
    })
  }

  window.addEventListener('resize', handleResize)

  // 清理事件监听器
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
  })
})

// 监听组件激活（从其他页面跳转过来时）
onActivated(async () => {
  console.log('🔄 组件激活，重新初始化状态')

  // 延迟一点时间确保路由完全切换
  setTimeout(async () => {
    // 强制重新初始化所有响应式变量
    console.log('🔧 强制重新初始化响应式变量')

    // 检查是否有任何搜索结果需要保持
    const hasAnySearchResult =
      vinSearchResult.value ||
      oeSearchResult.value ||
      carSearchResult.value ||
      reachSearchResult.value ||
      referenceSearchResult.value

    if (hasAnySearchResult) {
      // 如果有任何搜索结果，保持它们并只重新初始化表格状态
      console.log('🔄 保持现有搜索结果，只重新初始化表格状态')

      // 如果有Reach搜索结果，强制重新赋值以触发响应式更新
      if (reachSearchResult.value) {
        const originalResult = reachSearchResult.value
        reachSearchResult.value = null

        nextTick(() => {
          reachSearchResult.value = { ...originalResult }
          checkAndFixReachSearchResult()

          nextTick(() => {
            resetTableFields()
            forceUpdateColumns.value++
            tableRenderKey.value++

            console.log('✅ 组件激活后强制更新完成')
          })
        })
      } else {
        // 重置表格状态但保持搜索结果
        resetTableFields()
        forceUpdateColumns.value++
        tableRenderKey.value++
        console.log('✅ 组件激活后保持搜索结果完成')
      }
    } else {
      await initializeComponent()
    }
  }, 100)
})

// 监听路由变化
watch(
  () => route.path,
  (newPath, oldPath) => {
    if (newPath !== oldPath && newPath.includes('/search/query')) {
      console.log('🛣️ 路由变化到查询页面:', { from: oldPath, to: newPath })

      // 延迟初始化，确保组件完全加载
      setTimeout(async () => {
        await initializeComponent()
      }, 200)
    }
  },
  { immediate: false }
)

// 监听搜索类型变化，重置表格状态
watch(
  searchType,
  (newType, oldType) => {
    if (newType !== oldType) {
      console.log('🔄 搜索类型变化:', { from: oldType, to: newType })
      nextTick(() => {
        resetTableFields()
      })
    }
  },
  { immediate: false }
)

// 监听搜索结果变化，确保表格正确显示
watch(
  [oeSearchResult, reachSearchResult, carSearchResult],
  (newValues, oldValues) => {
    // 检查是否有实际的数据变化
    const hasDataChange = newValues.some((newVal, index) => {
      const oldVal = oldValues?.[index]
      return JSON.stringify(newVal?.data) !== JSON.stringify(oldVal?.data)
    })

    if (hasDataChange) {
      nextTick(() => {
        resetTableFields()
        // 强制重新渲染表格
        tableRenderKey.value++
      })
    }
  },
  { deep: true, immediate: false }
)

// OE搜索结构表格列
const allCarColumns = [
  { title: '车型ID', key: 'vehicle_id', width: 120 },
  { title: '品牌', key: 'brand', width: 100 },
  { title: '系列', key: 'series', width: 100 },
  { title: '车型', key: 'model', width: 120 },
  { title: '年款', key: 'model_year', width: 80 },
  { title: '发动机', key: 'engine_code', width: 120 },
  { title: '变速箱', key: 'trans_type', width: 150 },
  { title: '排量', key: 'displacement', width: 80 },
  { title: '燃料类型', key: 'fuel_type', width: 100 },
  { title: '驱动方式', key: 'driver_type', width: 100 },
  { title: 'VIO', key: 'vio', width: 80 },
  { title: '来源', key: 'source', width: 100 },
]

// 初始化车型表格字段选择
const initCarTableFields = () => {
  return allCarColumns.map((col) => col.key)
}

const carTableFields = ref(initCarTableFields())

// 创建一个强制更新的响应式变量
const forceUpdateColumns = ref(0)

const carTableColumns = computed(() => {
  // 触发强制更新依赖
  forceUpdateColumns.value

  console.log('🔄 重新计算carTableColumns:', {
    searchType: searchType.value,
    hasOeData: !!oeSearchResult.value?.data?.car_info?.length,
    hasReachData: !!reachSearchResult.value?.data?.car_info?.length,
    carTableFieldsLength: carTableFields.value?.length,
    forceUpdateColumns: forceUpdateColumns.value,
  })

  // 确保carTableFields有值
  const fields =
    carTableFields.value && carTableFields.value.length > 0
      ? carTableFields.value
      : initCarTableFields()

  // 获取当前显示的数据（根据搜索类型）
  let currentData = []
  if (searchType.value === 'OE' && oeSearchResult.value?.data?.car_info) {
    currentData = oeSearchResult.value.data.car_info
  } else if (searchType.value === 'Reach' && reachSearchResult.value?.data?.car_info) {
    currentData = reachSearchResult.value.data.car_info
  }

  console.log('📊 当前数据状态:', {
    searchType: searchType.value,
    currentDataLength: currentData.length,
    fields: fields,
  })

  // 如果没有数据，返回基础列配置
  if (currentData.length === 0) {
    return allCarColumns
      .filter((col) => fields.includes(col.key))
      .map((col) => ({
        ...col,
        filter: false,
        filterOptions: [],
        filterMultiple: true,
      }))
  }

  return allCarColumns
    .filter((col) => fields.includes(col.key))
    .map((col) => {
      // 为了实现联动筛选，基于其他字段的筛选条件来生成当前字段的选项
      let sourceData = currentData
      if (Object.keys(tableFilters.value).length > 0) {
        // 应用除当前字段外的所有筛选条件
        sourceData = currentData.filter((row) => {
          return Object.keys(tableFilters.value).every((key) => {
            // 跳过当前字段，避免循环依赖
            if (key === col.key) return true
            const filterValues = tableFilters.value[key]
            if (!filterValues || filterValues.length === 0) return true
            // 确保数据类型一致性
            const rowValue = row[key]
            const rowValueStr =
              rowValue !== null && rowValue !== undefined ? String(rowValue).trim() : ''
            return filterValues.includes(rowValueStr)
          })
        })
      }
      const uniqueValues = sourceData.length > 0 ? getColumnUniqueValues(sourceData, col.key) : []

      return {
        ...col,
        // 添加筛选功能 - 使用Naive UI内置筛选
        filter: uniqueValues.length > 0 ? true : false,
        filterOptions: uniqueValues.map((value) => ({
          label: String(value),
          value: String(value).trim(), // 确保筛选选项的值也是字符串并去除空格
        })),
        filterMultiple: true,
      }
    })
})
const oeReplacementColumns = [
  { title: 'OE号', key: 'oe' },
  { title: '替代号', key: 'replace_oe' },
  { title: '替代关系', key: 'replace_relation' },
  { title: '替代备注', key: 'replace_remark' },
]
const oeReachColumns = [
  { title: 'OE号', key: 'oe', width: 150 },
  {
    title: 'Reach号',
    key: 'reach_number',
    width: 200,
    render(row) {
      return h(
        'a',
        {
          style: 'color: #10b981; cursor: pointer;',
          onClick: () => handleGroupClick(row.reach_number),
        },
        row.reach_number
      )
    },
  },
  { title: '适配备注', key: 'reamrk', width: 200 },
]

// Reach产品基础信息动态分组（3列）
const productInfoEntries = computed(() => {
  const info = reachSearchResult.value?.data?.product_info || {}
  return Object.entries(info).map(([key, value]) => ({ key, value }))
})

// Reach图片相关计算属性
const reachProductPhotoList = computed(() => reachSearchResult.value?.data?.photo_list || [])
const reachProductDiagram = computed(() => reachSearchResult.value?.data?.diagram || '')

// Reach表格动态列
const reachTableColumns = [
  { title: 'Reach号', key: 'reach_number', width: 150 },
  {
    title: '零件号',
    key: 'PART_NUMBER',
    width: 150,
    render(row) {
      return h(
        'a',
        {
          style: 'color: #3b82f6; cursor: pointer;',
          onClick: () => handleOeClick(row.oe),
        },
        row.oe
      )
    },
  },
  { title: '标准标签', key: 'standard_label', width: 120 },
  { title: '来源', key: 'source', width: 100 },
  { title: '备注', key: 'remark', width: 150 },
]

const searchTabs = [
  { label: 'VIN', value: 'VIN' },
  { label: '车辆搜索', value: 'Car' },
  { label: 'OE', value: 'OE' },
  { label: 'REACH号', value: 'Reach' },
  { label: '参考号', value: 'Reference' },
]

// 车型选择弹窗表格列定义
const carSelectionColumns = [
  { title: '品牌', key: 'brand', width: 120 },
  { title: '车系', key: 'series', width: 120 },
  { title: '车型', key: 'models', width: 150 },
  { title: '年款', key: 'produced_year', width: 100 },
  { title: '制造商', key: 'manufacturers', width: 150 },
  { title: '发动机型号', key: 'engine_model', width: 120 },
  { title: '排量', key: 'displacement', width: 100 },
  { title: '燃料类型', key: 'fuel_type', width: 100 },
  { title: '变速箱', key: 'transmission_description', width: 150 },
  { title: '驱动方式', key: 'drive_mode', width: 100 },
]
</script>

<style scoped>
.search-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位，为搜索结果提供定位上下文 */
  /* 确保容器可以扩展以容纳超出背景图片的内容 */
  overflow: visible;
  height: auto;
  padding-bottom: 60px;
}

/* 当没有搜索结果时，确保搜索背景区域有合适的高度 */
.search-container:not(:has(.search-results-overlay)) .search-header-bg {
  min-height: calc(100vh - 120px);
}

.search-header-bg {
  width: 100%;
  /* 使用系统主色调的渐变背景设计 */
  background: linear-gradient(135deg, #21438c 0%, #1a3570 100%);
  /* 添加微妙的纹理效果 */
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  padding: 48px 0 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* flex: 1; */
  /* min-height: calc(100vh - 120px); */ /* 移除固定高度，让背景区域根据内容自适应 */
  position: relative;
  overflow: visible;
  z-index: 1;
  /* 添加动画效果 */
  transition: all 0.3s ease;
}
.search-tabs {
  display: flex;
  justify-content: center;
  gap: 2px; /* 进一步减小标签页之间的间隔 */
  width: 900px;
  margin-bottom: 0;
  transform: translateX(-60px); /* 向左移动，使VIN标签与搜索框左边对齐 */
  /* 添加标签页容器动画 */
  animation: fadeInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 标签页出现动画 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-60px) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-60px) translateY(0);
  }
}

/* 为标签页添加更好的视觉层次 */
.search-tab:not(.active) {
  /* 未激活标签页的额外样式 */
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(33, 67, 140, 0.2);
}

.search-tab:not(.active):hover {
  /* 未激活标签页悬停时的样式 */
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(33, 67, 140, 0.4);
}
.search-tab {
  min-width: 100px;
  max-width: 140px;
  text-align: center;
  font-size: 15px;
  font-weight: 600;
  padding: 10px 8px;
  border-radius: 12px 12px 0 0;
  /* 未激活状态：更明显的背景色，确保可见性 */
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  cursor: pointer;
  color: #21438c; /* 使用系统主色调，确保文字清晰可见 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(33, 67, 140, 0.3);
  position: relative;
  overflow: hidden;
  /* 添加阴影增强可见性 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(33, 67, 140, 0.1), transparent);
  transition: left 0.5s;
}

.search-tab:hover::before {
  left: 100%;
}

.search-tab:hover {
  background: rgba(255, 255, 255, 0.85);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(33, 67, 140, 0.2);
  border-color: rgba(33, 67, 140, 0.5);
  color: #1a3570;
}

.search-tab.active {
  /* 激活状态：使用系统主色调 */
  background: linear-gradient(135deg, #21438c 0%, #1a3570 100%);
  color: #ffffff;
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(33, 67, 140, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  font-weight: 700;
}

.search-tab.active::before {
  display: none; /* 激活状态不需要光泽效果 */
}
.search-panel {
  width: 900px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 16px 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  padding: 32px 0 28px 0;
  display: flex;
  justify-content: center;
  margin-bottom: 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-top: none;
  position: relative;
  /* 添加搜索面板动画 */
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

/* 搜索面板出现动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* 搜索结果覆盖层 - 现代玻璃拟态设计 */
.search-results-overlay {
  /* 改为相对定位，不再脱离文档流 */
  position: relative;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1100px;
  max-width: 95vw;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 10;
  min-height: 500px;
  margin: 10px 0 40px 0; /* 上下边距 */
  overflow-y: auto;
  max-height: none;
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 搜索结果出现动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.result-area {
  width: 1100px;
  margin: 40px auto 0 auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  flex-shrink: 0;
  padding: 32px 40px;
}

.vin-result-card {
  margin-bottom: 16px;
}

.vin-info-header {
  margin-bottom: 12px;
  padding: 12px 0 0 0;
}

.vin-info-main {
  display: flex;
  gap: 32px;
  font-size: 20px;
  font-weight: bold;
  color: #2d3a4b;
  margin-bottom: 6px;
}

.vin-info-brand {
  color: #3b82f6;
}

.vin-info-model {
  color: #6366f1;
}

.vin-info-year {
  color: #10b981;
}

.vin-info-vin {
  color: #64748b;
  font-size: 16px;
  font-weight: normal;
}

.vin-info-sub {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #64748b;
}

.vin-desc {
  margin-bottom: 12px;
}

.vin-sub-card {
  margin-bottom: 12px;
}

/* 搜索按钮样式优化 - 使用系统主色调 */
.n-input .n-input__suffix .n-button {
  height: 36px;
  padding: 0 24px;
  margin-right: -12px;
  background: linear-gradient(135deg, #21438c 0%, #1a3570 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(33, 67, 140, 0.3);
}

.n-input .n-input__suffix .n-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 67, 140, 0.4);
  background: linear-gradient(135deg, #1a3570 0%, #153058 100%);
}

.n-input .n-input__suffix .n-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(33, 67, 140, 0.3);
}

/* 车型搜索的搜索和重置按钮样式 */
.car-search-row .n-button {
  height: 36px;
  padding: 0 24px;
  border-radius: 8px;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.car-search-row .n-button--primary {
  background: linear-gradient(135deg, #21438c 0%, #1a3570 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(33, 67, 140, 0.3);
}

.car-search-row .n-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 67, 140, 0.4);
  background: linear-gradient(135deg, #1a3570 0%, #153058 100%);
}

.car-search-row .n-button:not(.n-button--primary) {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(33, 67, 140, 0.3);
  color: #21438c;
}

.car-search-row .n-button:not(.n-button--primary):hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: #21438c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 67, 140, 0.2);
}

.oe-detail-card {
  margin-bottom: 24px;
}
.oe-basic-info-section {
  margin-bottom: 24px;
}
.info-item {
  margin-bottom: 18px;
}
.info-label {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.info-value {
  font-size: 15px;
  color: #666;
  padding: 6px 0;
}
.oe-section-header {
  font-weight: bold;
  font-size: 16px;
  margin: 24px 0 12px 0;
}
.table-scroll-x {
  overflow-x: auto;
  margin-bottom: 12px;
  width: 100%;
}
.table-scroll-x .n-data-table {
  min-width: 900px;
  width: 100%;
}

/* 确保表格在不同分辨率下正常显示 */
@media (max-width: 1200px) {
  .table-scroll-x .n-data-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .table-scroll-x .n-data-table {
    min-width: 600px;
  }
}
.field-select-trigger {
  display: inline-flex;
  align-items: center;
  color: #1765d5;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  margin-left: 18px;
  user-select: none;
}
.field-select-text {
  margin-left: 2px;
}
.field-checkbox-group {
  display: flex;
  flex-direction: column;
  min-width: 140px;
  padding: 8px 12px;
  gap: 8px;
}
.iconfont.icon-menu {
  font-style: normal;
}
.reach-detail-card {
  margin-bottom: 24px;
}
.reach-basic-info-section {
  margin-bottom: 24px;
}
.reach-desc .n-descriptions-table {
  table-layout: fixed;
}
.reach-desc .n-descriptions-item-label {
  font-weight: 600;
  color: #2d3a4b;
  background: #f7f8fa;
  text-align: right;
}
.reach-desc .n-descriptions-item-content {
  color: #333;
  background: #fff;
  text-align: left;
}
.reach-desc .n-descriptions-row:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

/* 车型搜索表单样式 */
.car-search-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 800px;
}

.car-search-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 车型详情和OE详情样式 */
.car-detail-section,
.oe-detail-section {
  margin-top: 16px;
}

.detail-card {
  margin-bottom: 16px;
}

/* 输入框样式优化 */
.search-panel .n-input {
  border-radius: 12px;
  overflow: hidden;
}

.search-panel .n-input .n-input__input-el {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(33, 67, 140, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-panel .n-input:hover .n-input__input-el {
  border-color: rgba(33, 67, 140, 0.4);
  box-shadow: 0 4px 12px rgba(33, 67, 140, 0.1);
}

.search-panel .n-input.n-input--focus .n-input__input-el {
  border-color: #21438c;
  box-shadow: 0 0 0 3px rgba(33, 67, 140, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* 下拉选择框样式优化 */
.car-search-form .n-select {
  border-radius: 10px;
}

.car-search-form .n-base-selection {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(33, 67, 140, 0.2);
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.car-search-form .n-base-selection:hover {
  border-color: rgba(33, 67, 140, 0.4);
  box-shadow: 0 2px 8px rgba(33, 67, 140, 0.1);
}

.car-search-form .n-base-selection.n-base-selection--focus {
  border-color: #21438c;
  box-shadow: 0 0 0 2px rgba(33, 67, 140, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* 车型选择弹窗样式 */
.car-selection-content {
  padding: 16px 0;
}

.car-selection-tip {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f0f8ff;
  border: 1px solid #d1e7ff;
  border-radius: 8px;
  color: #1a3570;
  font-size: 14px;
  line-height: 1.5;
}

.car-selection-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 车型选择表格样式 */
.car-selection-table :deep(.n-data-table-tbody .n-data-table-tr) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.car-selection-table :deep(.n-data-table-tbody .n-data-table-tr:hover) {
  background-color: #f0f8ff;
}

.car-selection-table :deep(.n-data-table-tbody .n-data-table-tr:hover .n-data-table-td) {
  background-color: transparent;
}

/* 车型搜索条件禁用状态样式 */
.car-search-form .n-base-selection.n-base-selection--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.car-search-form .n-button.n-button--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 图片预览样式 */
.image-preview-modal .n-modal {
  padding: 0;
}

.image-preview-wrapper {
  display: flex;
  flex-direction: column;
  height: 95vh;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f5f5f5;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
}

.preview-counter {
  font-size: 14px;
  color: #666;
}

.preview-controls {
  display: flex;
  gap: 8px;
}

.image-preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  background: #fff;
  user-select: none;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.1s ease-out;
  transform-origin: center center;
}

.preview-footer {
  padding: 12px 20px;
  background: #f5f5f5;
  border-top: 1px solid #e0e0e0;
}

.preview-tips {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #666;
  flex-wrap: wrap;
}

.preview-tips span:first-child {
  color: #ffd700;
}

/* Reach图片按钮样式 */
.reach-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.reach-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reach-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.available-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
}

.available-status.available {
  color: #52c41a;
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.available-status.unavailable {
  color: #ff4d4f;
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.reach-image-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
}

.reach-image-buttons .n-button {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.reach-image-buttons .n-button:hover:not(.n-button--disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 67, 140, 0.2);
}

.reach-image-buttons .n-button.n-button--disabled {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #bfbfbf !important;
  cursor: not-allowed;
}

.reach-image-buttons .n-button.n-button--disabled .n-icon {
  color: #bfbfbf !important;
}

/* Excel风格表格筛选样式 */
.table-filter-dropdown {
  width: 250px;
  max-height: 300px;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 500;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 6px;
}

.filter-list {
  max-height: 200px;
  overflow-y: auto;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  gap: 8px;
}

.filter-label {
  font-size: 13px;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.filter-icon {
  color: #999;
  transition: color 0.2s;
}

.filter-icon.active {
  color: #21438c;
}

/* 筛选状态指示器 */
.n-data-table-th.filtered {
  background-color: #f0f8ff !important;
}

.n-data-table-th.filtered .n-data-table-th__title {
  color: #21438c !important;
  font-weight: 600 !important;
}

/* 筛选工具栏样式 */
.filter-toolbar {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.filter-tags {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  margin-right: 4px;
}

.filter-tag {
  margin: 2px 0;
}
</style>
